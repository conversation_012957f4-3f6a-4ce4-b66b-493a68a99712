<?php

use App\Http\Controllers\AdjustmentPointController;
use App\Http\Controllers\Auth\AuthController;
use App\Http\Controllers\Auth\EmailVerificationController;
use App\Http\Controllers\ClubController;
use App\Http\Controllers\EmailVerifyController;
use App\Http\Controllers\FileController;
use App\Http\Controllers\GroupController;
use App\Http\Controllers\JWTController;
use App\Http\Controllers\LiveKitController;
use App\Http\Controllers\LocationController;
use App\Http\Controllers\MatchDetailController;
use App\Http\Controllers\MuxController;
use App\Http\Controllers\PaymentController;
use App\Http\Controllers\PermissionController;
use App\Http\Controllers\PhotoGalleryController;
use App\Http\Controllers\PlayerController;
use App\Http\Controllers\RegistrationController;
use App\Http\Controllers\ReleasesController;
use App\Http\Controllers\RoleController;
use App\Http\Controllers\S3Controller;
use App\Http\Controllers\ScheduleBreakController;
use App\Http\Controllers\ScheduleConfigController;
use App\Http\Controllers\ScheduleMatchController;
use App\Http\Controllers\ScheduleTimeSlotController;
use App\Http\Controllers\SeasonController;
use App\Http\Controllers\SeasonRefereeController;
use App\Http\Controllers\SendMessageController;
use App\Http\Controllers\SettingsController;
use App\Http\Controllers\SponsorController;
use App\Http\Controllers\StageController;
use App\Http\Controllers\StageMatchController;
use App\Http\Controllers\StageTeamController;
use App\Http\Controllers\StripeController;
use App\Http\Controllers\TeamCoachController;
use App\Http\Controllers\TeamController;
use App\Http\Controllers\TeamPlayerController;
use App\Http\Controllers\TeamsheetController;
use App\Http\Controllers\TournamentController;
use App\Http\Controllers\UserClubController;
use App\Http\Controllers\UserController;
use App\Http\Controllers\UserMatchStreamingController;
use App\Http\Controllers\UserMessageController;
use Illuminate\Support\Facades\Route;
use Laravel\Cashier\Http\Controllers\WebhookController;

/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
|
| Here is where you can register API routes for your application. These
| routes are loaded by the RouteServiceProvider within a group which
| is assigned the 'api' middleware group. Enjoy building your API!
|
*/

Route::middleware(['language'])->group(function () {
    // Route::post('/users', [UserController::class, 'index'])->name('users.index');
    // Route::post('editor', function(UserDataTableEditor $editor) {
    //     return $editor->process(request());
    // });
    Route::post('livestream-webhook', [LiveKitController::class, 'liveStreamWebHook']);
    Route::post('delete-role-webhook', [UserMatchStreamingController::class, 'deleteRoleWebhook']);
    Route::get('logosponsor-webhook', [SponsorController::class, 'getAllLogos']);
    Route::post('metadata-webhook', [SettingsController::class, 'updateMetadata']);

    // TEST UPLOAD IMAGE
    Route::group(['prefix' => 's3'], function () {
        Route::get('max-upload-size', [S3Controller::class, 'getMaxSizeUploadINI']);
        Route::get('', [S3Controller::class, 'getImageFromClient']);
        Route::post('', [S3Controller::class, 'uploadImageFromClient']);
        Route::post('file', [S3Controller::class, 'uploadFileFromClient']);
        Route::post('delete', [S3Controller::class, 'deleteImageFromClient']);
    });
    // AUTH
    Route::group(['prefix' => 'auth'], function () {

        Route::get('verify-email/{id}/{hash}', [EmailVerificationController::class, 'verify'])
            ->middleware(['signed', 'throttle:6,1'])
            ->name('verification.verify'); // http://localhost:8000/api/auth/verify-email/1/2

        Route::middleware('guest')->group(function () {
            Route::post('register', [AuthController::class, 'register']);
            Route::post('login', [AuthController::class, 'login']);
            Route::post('login-as-guest', [AuthController::class, 'loginAsGuest']); //http://localhost:8000/api/auth/login-as-guest
            Route::post('forgot-password', [AuthController::class, 'forgotPassword']);
            Route::post('verify-2fa', [AuthController::class, 'verify2fa']);
            Route::post('reset-2fa', [AuthController::class, 'reset2fa']);
        });

        Route::middleware('auth:api')->group(function () {
            Route::post('logout', [AuthController::class, 'logout']);
            Route::post('logout-as-guest', [AuthController::class, 'logoutAsGuest']);
            Route::get('send-verification-email', [EmailVerificationController::class, 'resend']);
            Route::get('get-info', [AuthController::class, 'userInfo']);
            Route::post('change-password', [AuthController::class, 'changePassword']);
            Route::get('generate-2fa-secret', [AuthController::class, 'generate2faSecret']);
            Route::post('enable-2fa', [AuthController::class, 'enable2fa']);
            Route::post('disable-2fa', [AuthController::class, 'disable2fa']);
        });
    });

    // USER
    Route::group(['prefix' => 'users'], function () {
        Route::group(['middleware' => ['auth:api']], function () {
            Route::post('all', [UserController::class, 'all']);
            Route::post('delete', [UserController::class, 'delete']);
            Route::post('editor', [UserController::class, 'editor']);
            Route::put('update', [UserController::class, 'update']);
            Route::post('update-language', [
                UserController::class,
                'updateUserLanguage',
            ]);
            Route::post('role', [UserController::class, 'getUserByRole']);
            Route::get('reset-db', [UserController::class, 'resetDatabase']);
            Route::get('send-active', [UserController::class, 'sendNotiActive']);
            Route::get('favourite-clubs', [UserController::class, 'getFavouriteClubs']);
            Route::post('toggle-follow-club', [UserController::class, 'toggleFavouriteClub']);
            Route::get('favourite-teams', [UserController::class, 'getFavouriteTeams']);
            Route::post('toggle-follow-team', [UserController::class, 'toggleFavouriteTeam']);
            Route::get('by-email', [UserController::class, 'getUserByEmail']);
            Route::post('coaches', [UserController::class, 'getTeamCoaches']);
            Route::post('accept-policy', [UserController::class, 'acceptPolicy']);
        });
        Route::get('referee', [UserController::class, 'getReferee']);
    });

    // SEASON
    Route::group(['prefix' => 'seasons'], function () {
        // API with Auth
        Route::group(['middleware' => ['auth:api']], function () {
            Route::post('all', [SeasonController::class, 'all']);
            Route::get('all-active', [SeasonController::class, 'getSeasonsIsActive']); // http://localhost:8000/api/season/all-active
            Route::post('editor', [SeasonController::class, 'editor']);
            Route::post('latest', [SeasonController::class, 'getLatestSeasons']);
            Route::get('current-season', [SeasonController::class, 'getCurrentSeason']); // http://localhost:8000/api/season/current-season
            Route::get('{season_id}', [SeasonController::class, 'getSeasonById']);       // http://localhost:8000/api/season/1
            Route::post('{season_id}/matches', [SeasonController::class, 'getSeasonMatches']);
            Route::get('{season_id}/tournament-options/{group_id}', [TournamentController::class, 'optionsTournaments']);
            Route::get('{season_id}/tournaments', [TournamentController::class, 'getTournamentBySeasonId']);
            Route::get('', [SeasonController::class, 'getSeasons']);
            Route::get('{season_id}/manage-teams', [SeasonController::class, 'getTeamsInSeason2Manage']);
            Route::get('{season_id}/teams', [TeamController::class, 'getTeamsBySeasonId']);
            Route::post('{season_id}/teamsheets', [TeamSheetController::class, 'getTeamsheetBySeason']);
            Route::get('{season_id}/groups', [GroupController::class, 'getGroupsBySeasonId']);
            Route::put('{season_id}/limit-time-update-score', [SeasonController::class, 'limitTimeUpdateScore']);
            Route::get('{season_id}/referees', [SeasonRefereeController::class, 'getRefereesBySeasonId']);
            Route::delete('{season_id}/referees/{referee_id}', [SeasonRefereeController::class, 'deleteRefereeFromSeason']);
        });
    });

    Route::group(['prefix' => 'season-referees'], function () {
        Route::group(['middleware' => ['auth:api']], function () {
            Route::post('', [SeasonRefereeController::class, 'createNewReferee']);
        });
    });

    // GROUP
    Route::group(['prefix' => 'groups'], function () {
        // API with Auth
        Route::group(['middleware' => ['auth:api']], function () {
            Route::post('all', [GroupController::class, 'all']);
            Route::post('editor', [GroupController::class, 'editor']);
            Route::get('club/{club_id}', [GroupController::class, 'getGroupByClub']);
            Route::post('teams', [GroupController::class, 'getTeamsByGroupId']);
        });
    });

    // PLAYER
    Route::group(['prefix' => 'players'], function () {
        // API with Auth
        Route::group(['middleware' => ['auth:api']], function () {
            // Route::get('index', [PlayerController::class, 'index']);
            Route::get('of-user', [PlayerController::class, 'getPlayersByParent']);
            Route::get('of-user-season/{season_id}', [PlayerController::class, 'getPlayersByParentSeason']);
            Route::get('season-status/{season_id}', [PlayerController::class, 'getPlayersRegistrationStatus']);
            Route::post('update-information', [PlayerController::class, 'updatePlayerForValidate']); // http://localhost:8000/api/player/update-information
            Route::post('update-by-admin', [PlayerController::class, 'updatePlayerRegistrationByAdmin']);
            Route::get('season/{season_id}', [PlayerController::class, 'getPlayersRegisteredInSeason']);
            Route::get('report', [PlayerController::class, 'getRegistrationPlayersReport']);
            Route::get('request-update', [PlayerController::class, 'getPlayersAndReqUpdate']);
            Route::post('request-update', [PlayerController::class, 'requestUpdate']);
            Route::post('player-updates', [PlayerController::class, 'getPlayerUpdates']);
            Route::post('reject-updates', [PlayerController::class, 'rejectPlayerUpdate']);
            Route::post('approve-updates', [PlayerController::class, 'approvePlayerUpdate']);
        });
    });

    // CLUB
    Route::group(['prefix' => 'clubs'], function () {
        // API with Auth
        Route::group(['middleware' => ['auth:api']], function () {
            Route::post('all', [ClubController::class, 'all']);
            Route::post('editor', [ClubController::class, 'editor']);
            Route::get('all-active', [ClubController::class, 'getClubsActive']);
            Route::post('toggle-active', [ClubController::class, 'toggleActive']);
            Route::get('by-user', [ClubController::class, 'getClubByUser']);
            Route::get('{club_id}/users', [ClubController::class, 'getUsersByClub']);
            Route::post('users/create', [UserClubController::class, 'create']);
            Route::post('users/destroy', [UserClubController::class, 'destroy']);
            Route::post('{club_id}/coaches', [ClubController::class, 'getCoachesByClub']);
        });
    });

    // REGISTRATION
    Route::group(['prefix' => 'registrations'], function () {
        // API with Auth
        Route::post('editor', [RegistrationController::class, 'editor']);

        Route::group(['middleware' => ['auth:api']], function () {
            Route::post('add-new-player', [PlayerController::class, 'createByParent']);
            Route::post('re-register', [RegistrationController::class, 'reRegisterByParent']);
            Route::post('all', [RegistrationController::class, 'all']);
            Route::post('by-season-and-type', [RegistrationController::class, 'bySeasonAndType']);
            // assign new group
            Route::post('assign-new-group', [RegistrationController::class, 'assignNewGroup']);
            Route::post('editor', [RegistrationController::class, 'editor']);
            Route::post('create', [RegistrationController::class, 'create']);
            Route::post('season/{season_id}', [RegistrationController::class, 'getRegistrationsBySeason']); // http://localhost:8000/api/registration/season/1
            Route::post('validate-player', [PlayerController::class, 'validatePlayer']);
            Route::post('change-club', [RegistrationController::class, 'changeClub']);
            Route::post('club-group-approved', [RegistrationController::class, 'getRegistrationsByClubAndSeason']);
            Route::post('approve', [RegistrationController::class, 'approveRegistration']);
            Route::post('club-group-approved', [RegistrationController::class, 'getPlayersCanAssignToTeam']);
            Route::get('/suitable-groups/{registration_id}', [RegistrationController::class, 'requestToGetSuitableGroup']);
            Route::post('cancel', [RegistrationController::class, 'cancelRegistration']);
            Route::post('sync-invoices', [PaymentController::class, 'syncPaymentRegistrationStatus']);
            Route::post('send-reminder', [RegistrationController::class, 'sendReminder']);
            Route::post('message-reminder', [RegistrationController::class, 'messageReminder']);
            Route::post('export-to-excel', [RegistrationController::class, 'exportToExcel']);
            Route::post('assign-group', [RegistrationController::class, 'approveGroup']);
        });
    });

    // TEAM
    Route::group(['prefix' => 'teams'], function () {
        // API with Auth
        Route::group(['middleware' => ['auth:api']], function () {
            Route::get('auth-user', [TeamController::class, 'getTeamManageByAuth']);
            Route::get('{team_id}', [TeamController::class, 'getTeamById']);                            // http://localhost:8000/api/teams/1
            Route::get('{team_id}/is-club-manager', [TeamController::class, 'isClubManagerOfTeam']);    // http://localhost:8000/api/teams/1
            Route::get('season/{season_id}', [TeamController::class, 'getTeamsByUser']);                // http://localhost:8000/api/season/team/1/club/1
            Route::get('season/{season_id}/club/{club_id}', [TeamController::class, 'getTeamsByUser']); // http://localhost:8000/api/season/team/1/club/1
            Route::get('season/{season_id}/club/{club_id}/group/{group_id}', [TeamController::class, 'getTeamsByUser']);
            Route::get('group/{group_id}', [TeamController::class, 'getTeamsByGroupId']);
            Route::get('{team_id}/players', [TeamController::class, 'getPlayersByTeamId']);
            Route::get('{season_id}/unassign-players', [TeamController::class, 'getUnassignPlayersBySeasonId']);
            Route::post('assign-player', [TeamController::class, 'assignPlayerToTeam']);
            Route::post('remove-player', [TeamController::class, 'removePlayerFromTeam']);
            Route::post('import-teams', [TeamController::class, 'importTeamFromFile']);
            Route::get('download-template/{file_name}', [TeamController::class, 'downloadTemplate']);
            Route::post('not-in-stage', [StageTeamController::class, 'teamsNotInStage']);
            Route::post('in-stage', [StageTeamController::class, 'teamsInStage']);
            Route::post('editor', [TeamController::class, 'editor']);
            Route::post('{team_id}/players', [TeamController::class, 'getTeamPlayersById']);
            Route::post('{team_id}/coaches', [TeamController::class, 'getTeamCoachesById']);
            Route::post('coaches', [TeamController::class, 'getCoachAllClub']);
        });
    });

    // ROLE
    Route::group(['prefix' => 'roles'], function () {
        // API with Auth
        Route::group(['middleware' => ['auth:api']], function () {
            Route::post('all', [RoleController::class, 'all']);
            // Route::post('editor', [RoleController::class, 'editor']);
            Route::post('create', [RoleController::class, 'store']);
            Route::post('edit', [RoleController::class, 'update']);
            Route::post('delete', [RoleController::class, 'destroy']);
            Route::get('index', [RoleController::class, 'index']);
            Route::put('reset', [RoleController::class, 'resetRolePermissions']);
        });
    });

    // PERMISSION
    Route::group(['prefix' => 'permissions'], function () {
        // API with Auth
        Route::group(['middleware' => ['auth:api']], function () {
            // Route::post('all', [PermissionController::class, 'all']);
            // Route::post('editor', [PermissionController::class, 'editor']);
            Route::get('index', [PermissionController::class, 'index']);
        });
    });

    // TEAM PLAYERS
    Route::group(['prefix' => 'team-players'], function () {
        // API with Auth
        Route::group(['middleware' => ['auth:api']], function () {
            Route::post('in-team/{team_id}', [TeamPlayerController::class, 'inTeam']);
            Route::post('editor', [TeamPlayerController::class, 'editor']);
            Route::get('{team_id}/players', [TeamPlayerController::class, 'optionsPlayersByTeam']);
        });
    });

    /** Team coaches */
    Route::group(['prefix' => 'team-coaches'], function () {
        // API with Auth
        Route::group(['middleware' => ['auth:api']], function () {
            Route::post('editor', [TeamCoachController::class, 'editor']);
            Route::post('assign-new', [TeamCoachController::class, 'assignNewCoach']);
        });
    });

    // TEAM SHEETS
    Route::group(['prefix' => 'team-sheets'], function () {
        // API with Auth
        Route::group(['middleware' => ['auth:api']], function () {
            Route::post('all', [TeamSheetController::class, 'all']); // http://localhost:8000/api/team-sheets/all/1
            Route::post('by-season/{season_id}', [TeamSheetController::class, 'getTeamsheetBySeason']);
            Route::post('team/{team_id}', [TeamSheetController::class, 'getTeamsheetByTeamId']);
            Route::post('editor', [TeamSheetController::class, 'editor']);
            Route::post('submit', [TeamsheetController::class, 'submitTeamsheet']);
            Route::get('show/{id}', [TeamSheetController::class, 'show']);
        });
    });

    // TOURNAMENT
    Route::group(['prefix' => 'tournaments'], function () {
        // API with Auth
        Route::group(['middleware' => ['auth:api']], function () {
            Route::get('{tournament_id}', [TournamentController::class, 'getTournamentById']);
            Route::get('getAllTournaments', [TournamentController::class, 'getAllTournaments']);
            Route::post('all-in-group', [TournamentController::class, 'allInGroup']);
            Route::post('all-in-group-sort', [TournamentController::class, 'allInGroupSortByOrder']);
            Route::post('editor', [TournamentController::class, 'editor']);
            Route::post('stage/{stage_id}/match', [TournamentController::class, 'getMatchesByStageId']);
            Route::get('{tournament_id}/fixtures-results', [TournamentController::class, 'fixturesResults']);
            Route::get('{tournament_id}/scoreboard-matches', [TournamentController::class, 'scoreboardMatches']);
            Route::get('team/{team_id}/fixtures-results', [TournamentController::class, 'fixturesResultsByTeam']);
            Route::get('season/{season_id}/matches-user', [TournamentController::class, 'showMatchesInSeasonByUser']);
            Route::get('season/{season_id}/fixtures', [TournamentController::class, 'showFixturesResultsInSeason']);
            Route::get('{tournament_id}/knockout-matches', [TournamentController::class, 'knockOutMatches']);
            Route::get("{tournament_id}/groups", [TournamentController::class, 'getListGroupsByTournamentId']);
            Route::get('{tournament_id}/stages', [TournamentController::class, 'getStagesByTournamentId']);
        });

    });

    // STAGE
    Route::group(['prefix' => 'stages'], function () {
        // API with Auth
        Route::group(['middleware' => ['auth:api']], function () {
            Route::post('all-in-tournament/{tournament_id}', [StageController::class, 'allInTournament']);
            Route::post('editor', [StageController::class, 'editor']);
            Route::get('{stage_id}', [StageController::class, 'getStageById']);
            Route::post('auto-generate', [StageController::class, 'generateMatches']);
            Route::get('{stage_id}/table', [StageController::class, 'getTableByStageId']);
            Route::get('has-matches/{stage_id}', [StageController::class, 'hasMatches']);
            Route::post('check-scores', [StageController::class, 'checkStageMatchesScores']);
            Route::get("{stage_id}/referees", [StageController::class, 'getListRefereesByStageId']);
            Route::get('{stage_id}/can-update-leaderboard', [StageController::class, 'canUpdateLeaderboard']);
            Route::put('submit-order', [StageTeamController::class, 'submitTeamOrder']);
        });
    });

    // Route::group(['prefix' => 'plan-stage-matches'], function () {

    //     Route::post('', [PlanTournamentController::class, 'planMatches']);
    //     Route::post('submit', [PlanTournamentController::class, 'submitPlan']);
    // });

    Route::group(['prefix' => 'auto-schedule'], function () {

        Route::post('generate', [ScheduleMatchController::class, 'scheduleMatches']); // updated
        Route::post('add-break', [ScheduleBreakController::class, 'addBreak']);
        Route::put('update-break', [ScheduleBreakController::class, 'updateBreak']);

        Route::get('schedule-config', [ScheduleConfigController::class, 'getScheduleConfig']);    // updated
        Route::get('schedule-config/{config_id}', [ScheduleConfigController::class, 'getScheduleConfigById']);    // updated
        Route::put('schedule-config', [ScheduleConfigController::class, 'updateScheduleConfig']); // updated

        Route::delete('delete-schedule', [ScheduleTimeSlotController::class, 'deleteSchedule']); // updated

        Route::put('unschedule-time-slot', [ScheduleTimeSlotController::class, 'unScheduleTimeSlot']);
        Route::put('update-location-match', [ScheduleTimeSlotController::class, 'updateLocationMatch']);
        Route::put('update-referees', [ScheduleMatchController::class, 'updateReferees']);

        Route::get('{tournament_id}', [ScheduleTimeSlotController::class, 'getScheduleTimeSlots']);
        Route::get('{tournament_id}/conflicts', [ScheduleMatchController::class, 'getScheduleConflict']);
        Route::get('{tournament_id}/unscheduled-matches', [ScheduleTimeSlotController::class, 'getUnScheduledMatches']);
        Route::post('{tournament_id}/submit', [ScheduleMatchController::class, 'submitSchedule']);
        Route::delete('{tournament_id}/clear-schedule', [ScheduleTimeSlotController::class, 'clearScheduleOfTournament']);

        Route::put('{tournament_id}/update-schedule-status', [ScheduleConfigController::class, 'updateScheduleStatus']);
    });

    // STAGE TEAM
    Route::group(['prefix' => 'stage-teams'], function () {
        // API with Auth
        Route::group(['middleware' => ['auth:api']], function () {
            Route::post('all-in-stage/{stage_id}', [StageTeamController::class, 'allInStage']);
            Route::post('editor', [StageTeamController::class, 'editor']);
            Route::post('create', [StageTeamController::class, 'create']);
            Route::post('delete', [StageTeamController::class, 'destroy']);
            Route::post('edit-group', [StageTeamController::class, 'update']);
        });
    });

    // STAGE MATCH
    Route::group(['prefix' => 'stage-matches'], function () {
        // API with Auth
        Route::group(['middleware' => ['auth:api']], function () {
            Route::get('live-matches', [StageMatchController::class, 'getLiveMatches']);
            Route::get('streaming/{status?}', [StageMatchController::class, 'getStreamingMatches']);
            Route::post('all-in-stage/{stage_id}', [StageMatchController::class, 'allInStage']);
            Route::post('editor', [StageMatchController::class, 'editor']);
            Route::post('{match_id}/resetScore', [StageMatchController::class, 'resetScoreById']);
            Route::post('resetScore', [StageMatchController::class, 'resetScoreMultiple']);
            Route::get('{match_id}', [StageMatchController::class, 'show']);
            Route::get('{match_id}/details', [MatchDetailController::class, 'getMatchDetail']);
            Route::put('{match_id}/details', [MatchDetailController::class, 'update']);
            Route::delete('details/{match_detail_id}', [MatchDetailController::class, 'destroy']);
            Route::get('{match_id}/exist', [StageMatchController::class, 'checkMatchExists']);
            Route::post('update-broadcast', [StageMatchController::class, 'updateBroadcastId']);
            Route::post('update-score', [StageMatchController::class, 'updateScore']);
            Route::post('swap', [StageMatchController::class, 'swapTeams']);
            Route::post('update-broadcast-status', [StageMatchController::class, 'updateBroadcastStatus']);
            Route::post('update-broadcast-data', [StageMatchController::class, 'updateBroadcastData']);
            Route::get('by-season/{season_id}', [StageMatchController::class, 'getDashboardTable']);
            Route::get('dashboard-info/{season_id}', [StageMatchController::class, 'getSeasonDashboardInfo']);
            Route::post('check', [StageMatchController::class, 'checkAllMatches']);
            Route::get('rankings/{stage_id}', [StageMatchController::class, 'getRankings']);
            Route::get('tournament/{tournament_id}/matches', [StageMatchController::class, 'getKnockoutGroupMatch']);
            Route::post("assign-referees", [StageMatchController::class, 'assignReferees']);
        });
    });

    // Adjustment Points
    Route::group(['prefix' => 'adjustment-points'], function () {
        // API with Auth
        Route::group(['middleware' => ['auth:api']], function () {
            Route::post('all/{stage_id}', [AdjustmentPointController::class, 'all']);
            Route::post('editor', [AdjustmentPointController::class, 'editor']);
        });
    });

    // locations
    Route::group(['prefix' => 'locations'], function () {
        // API with Auth
        Route::group(['middleware' => ['auth:api']], function () {
            Route::get('all', [LocationController::class, 'all']);
            Route::post('all', [LocationController::class, 'all']);
            Route::post('editor', [LocationController::class, 'editor']);
            Route::post('getFixtures', [LocationController::class, 'getFixtures']);
            Route::post('generateIcs', [LocationController::class, 'generateIcs']);
        });
    });

    Route::group(['prefix' => 'photo-gallery'], function () {
        // API with Auth
        Route::group(['middleware' => ['auth:api']], function () {
            Route::post('all', [PhotoGalleryController::class, 'all']);
            Route::get('group/{group_id}', [PhotoGalleryController::class, 'getGalleryByGroup']);
            Route::post('create', [PhotoGalleryController::class, 'create']);
            Route::post('delete', [PhotoGalleryController::class, 'delete']);
        });
    });

    // Send Message
    Route::group(['prefix' => 'send-messages'], function () {
        // API with Auth
        Route::group(['middleware' => ['auth:api']], function () {
            Route::get('{message_id}', [SendMessageController::class, 'getMessage']);
            Route::post('season', [SendMessageController::class, 'bySeason']);
            Route::post('send', [SendMessageController::class, 'sendCustomMessage']);
            Route::post('{message_id}/details', [SendMessageController::class, 'messageDetails']);
            Route::delete('{message_id}', [SendMessageController::class, 'delete']);
            Route::post('send-validate', [SendMessageController::class, 'sendValidateAdminMessage']);
        });
    });

    // User messages
    Route::group(['prefix' => 'user-messages'], function () {
        // API with Auth
        Route::group(['middleware' => ['auth:api']], function () {
            Route::get('{user_id}', [UserMessageController::class, 'getUserMessages']);
            Route::get('mark-all-as-read/{user_id}', [UserMessageController::class, 'markAllAsRead']);
            Route::post('mark-as-read', [UserMessageController::class, 'markAsRead']);
        });
    });

    // Files
    Route::group(['prefix' => 'files'], function () {
        // API with Auth
        Route::group(['middleware' => ['auth:api']], function () {
            Route::post('editor', [FileController::class, 'editor']);
        });
    });

    // Settings
    Route::group(['prefix' => 'settings'], function () {
        Route::get('required-version', [SettingsController::class, 'getRequiredVersion']);
        Route::get('notifications', [SettingsController::class, 'getNotification']);
        Route::get('metadata', [SettingsController::class, 'getMetadata']);
        // API with Auth
        Route::group(['middleware' => ['auth:api']], function () {
            Route::get('init-json', [SettingsController::class, 'getInitJson']);
            Route::get('', [SettingsController::class, 'getSettings']);
            Route::post('', [SettingsController::class, 'updateSettings']);
            Route::post('update-tournaments-order', [TournamentController::class, 'updateOrder']);
        });
    });

    // settings without auth
    Route::group(['prefix' => 'settings-no-auth'], function () {
        Route::get('', [SettingsController::class, 'getSettings']);            // http://localhost:3000/api/settings-no-auth
        Route::post('accept-policy', [UserController::class, 'acceptPolicy']); // http://localhost:3000/api/settings-no-auth/accept-policy
    });

    // Release
    Route::group(['prefix' => 'releases'], function () {
        // API with Auth
        Route::group(['middleware' => ['auth:api']], function () {
            Route::post('all', [ReleasesController::class, 'all']);
            Route::post('editor', [ReleasesController::class, 'editor']);
            Route::get('', [ReleasesController::class, 'get']);
        });
        Route::get('towards_version/{towards_version}', [ReleasesController::class, 'getByTowardsVersion']);
    });

    // Invoice
    Route::group(['prefix' => 'invoices'], function () {
        // API with Auth
        Route::group(['middleware' => ['auth:api']], function () {
            Route::post('create_approve', [RegistrationController::class, 'createInvoiceApproveRegistration']);
        });
    });

    // Stripe
    Route::group(['prefix' => 'stripe'], function () {
        Route::post('webhooks', [WebhookController::class, 'handleWebhook']);
        // API with Auth
        Route::group(['middleware' => ['auth:api']], function () {
            Route::post('create-customer', [StripeController::class, 'createCustomer']);
            Route::get('invoice', [StripeController::class, 'getInvoices']);
            Route::post('checkout', [StripeController::class, 'checkout']);
            Route::post('sync-invoices-registration', [StripeController::class, 'syncPaymentRegistrationStripe']);
        });
    });

    Route::group(['prefix' => 'sponsors'], function () {
        // API with Auth
        Route::group(['middleware' => ['auth:api']], function () {
            Route::post('all', [SponsorController::class, 'all']);
            Route::get('', [SponsorController::class, 'get']);
            Route::get('{sponsor_id}', [SponsorController::class, 'getById']);
            Route::post('create', [SponsorController::class, 'create']);

            Route::post('update', [SponsorController::class, 'update']);
            Route::post('order', [SponsorController::class, 'updateOrder']);
            Route::delete('{sponsor_id}', [SponsorController::class, 'delete']);
        });
    });

    // Paypal
    //    Route::group(['prefix' => 'paypal'], function () {
    //        Route::post('webhooks', [PaypalController::class, 'handleWebhook']);
    //        // API with Auth
    ////        Route::group(['middleware' => ['auth:api']], function () {
    ////            Route::post('create-invoice', [PaypalController::class, 'createInvoice']);
    //        Route::get('invoice-details', [PaypalController::class, 'handleGetInvoiceDetailAPI']);
    //        Route::post('create-invoice', [PaypalController::class, 'handleCreateAndSendInvoiceAPI']);
    //        Route::post('send-invoice', [PaypalController::class, 'handleCreateAndSendInvoiceAPI']);
    //        Route::post('send-reminder', [PaypalController::class, 'sendReminder']);
    //        Route::post('mark-as-paid', [PaypalController::class, 'handleMarkAsPaidAPI']);
    ////        });
    //    });

    // Payments
    Route::group(['prefix' => 'payments'], function () {
        // API with Auth
        Route::group(['middleware' => ['auth:api']], function () {
            Route::post('all', [PaymentController::class, 'all']);
            Route::get('{payment_id}', [PaymentController::class, 'show']);
            Route::post('season/{season_id}', [PaymentController::class, 'getPaymentsBySeason']);
            Route::post('cancel-payment', [PaymentController::class, 'cancelPayment']); // http://localhost:8000/api/payments/cancel-payment
            Route::post('re-generate-invoices', [PaymentController::class, 'reGenerateInvoice']);
            Route::post('sync-invoices', [PaymentController::class, 'syncPayment'])->middleware('admin');
            Route::post('send-reminder', [PaymentController::class, 'sendReminder']);
            Route::post('refund-invoices', [PaymentController::class, 'refundInvoices']);
            Route::post('mark-as-paid-invoices', [PaymentController::class, 'markAsPaid']);
        });
    });

    // JWT
    Route::group(['prefix' => 'jwt'], function () {
        // API with Auth
        Route::group(['middleware' => ['auth:api']], function () {
            Route::post('create', [JWTController::class, 'generateJwt']);
        });
    });

    // Mux
    Route::group(['prefix' => 'mux'], function () {
        // API with Auth
        Route::group(['middleware' => ['auth:api']], function () {
            Route::post('create-studio', [MuxController::class, 'createStudio']);
            Route::post('generate-token', [MuxController::class, 'generateToken']);
            Route::post('create-playback-id', [MuxController::class, 'createPlaybackId']);
            Route::post('start-stream', [MuxController::class, 'goLive']);
            Route::post('stop-stream', [MuxController::class, 'stopLive']);
            Route::post('create-playback-asset', [MuxController::class, 'createPlaybackIdForAsset']);
            Route::post('retrieve-live-stream', [MuxController::class, 'retrieveLiveStream']);
            Route::post('init-studio-stream', [MuxController::class, 'initLiveStream']);
        });
    });

    // LiveKit
    Route::group(['prefix' => 'livekit'], function () {
        // API with Auth
        Route::group(['middleware' => ['auth:api']], function () {
            Route::post('generate-token', [LiveKitController::class, 'generateToken']);
            Route::post('delete-room', [LiveKitController::class, 'deleteRoom']);
            // updateRoomMetadata
            Route::post('update-room-metadata', [LiveKitController::class, 'updateRoomMetadata']);
            Route::post('start-web-egress', [LiveKitController::class, 'startWebEgress']);
            Route::post('start-web-egress-hls', [LiveKitController::class, 'startWebEgressHLS']);
            Route::post('start-web-egress-rtmp', [LiveKitController::class, 'startWebEgressRTMP']);
            Route::post('start-custom-egress-hls', [LiveKitController::class, 'startCustomEgressHLS']);
            Route::post('stop-egress', [LiveKitController::class, 'stopEgress']);
            Route::get('list-egress', [LiveKitController::class, 'listEgress']);
        });
        Route::get('list-rooms', [LiveKitController::class, 'listRooms']);
    });

    // User Match Streaming
    Route::group(['prefix' => 'match-streaming'], function () {
        // API with Auth
        Route::group(['middleware' => ['auth:api']], function () {
            Route::get('users', [UserMatchStreamingController::class, 'userToAssign']);
            Route::post('assign', [UserMatchStreamingController::class, 'assignUsers']);
            Route::get('/{match_id}/show', [UserMatchStreamingController::class, 'show']);
            Route::get('/{match_id}', [UserMatchStreamingController::class, 'userMatchStreaming']);
            Route::post('/assign-role', [UserMatchStreamingController::class, 'assignRoleToUser']);
            Route::get('/match-user/{user_id}', [StageMatchController::class, 'getUserLiveMatches']);
        });
    });

    // Check email verification
    Route::group(['prefix' => 'email'], function () {
        Route::group(['middleware' => ['auth:api']], function () {
            Route::post('send-verify', [EmailVerifyController::class, 'sendEmail']);
            Route::post('resend-verify', [EmailVerifyController::class, 'resendEmail']);
        });
        Route::get('verify-email/{id}/{hash}', [EmailVerifyController::class, 'confirmVerifyEmail'])
            ->name('verification.verify');
    });
});
