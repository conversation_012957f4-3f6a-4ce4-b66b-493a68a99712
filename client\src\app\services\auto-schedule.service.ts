import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { environment } from 'environments/environment';

@Injectable({
    providedIn: 'root'
})
export class AutoScheduleService {

    constructor(private _http: HttpClient) {
    }

    getScheduleMatches(tournamentId: string) {
        return this._http.get<any>(`${environment.apiUrl}/auto-schedule/${tournamentId}`);
    }

    getListUnScheduledMatches(tournamentId: string) {
        return this._http.get<any>(`${environment.apiUrl}/auto-schedule/${tournamentId}/unscheduled-matches`);
    }

    scheduleTournament(formData) {
        return this._http.post<any>(`${environment.apiUrl}/auto-schedule/generate`, formData);
    }

    getScheduleConfigById(configId: number | string) {
        return this._http.get<any>(`${environment.apiUrl}/auto-schedule/schedule-config/${configId}`);
    }

    updateScheduleConfig(formData) {
        return this._http.put<any>(`${environment.apiUrl}/auto-schedule/schedule-config`, formData);
    }

    deleteSchedule(tournamentId: string, locationId: string, date: string) {
        return this._http.delete<any>(`${environment.apiUrl}/auto-schedule/delete-schedule?tournament_id=${tournamentId}&location=${locationId}&date=${date}`);
    }

    unScheduleMatch(timeSlotId: string | number) {
        return this._http.put<any>(`${environment.apiUrl}/auto-schedule/unschedule-time-slot`, {
            time_slot_id: timeSlotId
        });
    }

    updateLocationMatch(updateData: {
        new_index: { [key: string | number]: number };
        location_id: number;
        prev_location_id: number;
        stage_id: number;
        prev_stage_id: number;
        tournament_id: number;
    }) {
        return this._http.put(`${environment.apiUrl}/auto-schedule/update-location-match`, updateData);
    }

    addBreak(formModel) {
        return this._http.post<any>(`${environment.apiUrl}/auto-schedule/add-break`, formModel);
    }

    updateBreak(formModel) {
        return this._http.put<any>(`${environment.apiUrl}/auto-schedule/update-break`, formModel);
    }

    updateMatchReferee(formModel) {
        return this._http.put(`${environment.apiUrl}/auto-schedule/update-referees`, formModel);
    }

    clearSchedule(tournamentId: string) {
        return this._http.delete<any>(`${environment.apiUrl}/auto-schedule/${tournamentId}/clear-schedule`);
    }

    updateTournamentScheduleStatus(tournamentId: string) {
        return this._http.put<any>(`${environment.apiUrl}/auto-schedule/${tournamentId}/update-schedule-status`, {});
    }
}
