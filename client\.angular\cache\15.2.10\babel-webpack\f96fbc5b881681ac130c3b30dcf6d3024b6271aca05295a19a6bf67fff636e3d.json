{"ast": null, "code": "import { environment } from 'environments/environment';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common/http\";\nexport class AutoScheduleService {\n  constructor(_http) {\n    this._http = _http;\n  }\n  getScheduleMatches(tournamentId) {\n    return this._http.get(`${environment.apiUrl}/auto-schedule/${tournamentId}`);\n  }\n  getListUnScheduledMatches(tournamentId) {\n    return this._http.get(`${environment.apiUrl}/auto-schedule/${tournamentId}/unscheduled-matches`);\n  }\n  scheduleTournament(formData) {\n    return this._http.post(`${environment.apiUrl}/auto-schedule/generate`, formData);\n  }\n  getScheduleConfig(tournamentId, locationId) {\n    return this._http.get(`${environment.apiUrl}/auto-schedule/schedule-config?tournament_id=${tournamentId}&location=${locationId}`);\n  }\n  updateScheduleConfig(formData) {\n    return this._http.put(`${environment.apiUrl}/auto-schedule/schedule-config`, formData);\n  }\n  deleteSchedule(tournamentId, locationId) {\n    return this._http.delete(`${environment.apiUrl}/auto-schedule/delete-schedule?tournament_id=${tournamentId}&location=${locationId}`);\n  }\n  unScheduleMatch(timeSlotId) {\n    return this._http.put(`${environment.apiUrl}/auto-schedule/unschedule-time-slot`, {\n      time_slot_id: timeSlotId\n    });\n  }\n  updateLocationMatch(updateData) {\n    return this._http.put(`${environment.apiUrl}/auto-schedule/update-location-match`, updateData);\n  }\n  addBreak(formModel) {\n    return this._http.post(`${environment.apiUrl}/auto-schedule/add-break`, formModel);\n  }\n  updateBreak(formModel) {\n    return this._http.put(`${environment.apiUrl}/auto-schedule/update-break`, formModel);\n  }\n  updateMatchReferee(formModel) {\n    return this._http.put(`${environment.apiUrl}/auto-schedule/update-referees`, formModel);\n  }\n  clearSchedule(tournamentId) {\n    return this._http.delete(`${environment.apiUrl}/auto-schedule/${tournamentId}/clear-schedule`);\n  }\n  updateTournamentScheduleStatus(tournamentId) {\n    return this._http.put(`${environment.apiUrl}/auto-schedule/${tournamentId}/update-schedule-status`, {});\n  }\n  static #_ = this.ɵfac = function AutoScheduleService_Factory(t) {\n    return new (t || AutoScheduleService)(i0.ɵɵinject(i1.HttpClient));\n  };\n  static #_2 = this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n    token: AutoScheduleService,\n    factory: AutoScheduleService.ɵfac,\n    providedIn: 'root'\n  });\n}", "map": {"version": 3, "mappings": "AAEA,SAAQA,WAAW,QAAO,0BAA0B;;;AAKpD,OAAM,MAAOC,mBAAmB;EAE5BC,YAAoBC,KAAiB;IAAjB,UAAK,GAALA,KAAK;EACzB;EAEAC,kBAAkB,CAACC,YAAoB;IACnC,OAAO,IAAI,CAACF,KAAK,CAACG,GAAG,CAAM,GAAGN,WAAW,CAACO,MAAM,kBAAkBF,YAAY,EAAE,CAAC;EACrF;EAEAG,yBAAyB,CAACH,YAAoB;IAC1C,OAAO,IAAI,CAACF,KAAK,CAACG,GAAG,CAAM,GAAGN,WAAW,CAACO,MAAM,kBAAkBF,YAAY,sBAAsB,CAAC;EACzG;EAEAI,kBAAkB,CAACC,QAAQ;IACvB,OAAO,IAAI,CAACP,KAAK,CAACQ,IAAI,CAAM,GAAGX,WAAW,CAACO,MAAM,yBAAyB,EAAEG,QAAQ,CAAC;EACzF;EAEAE,iBAAiB,CAACP,YAAoB,EAAEQ,UAAkB;IACtD,OAAO,IAAI,CAACV,KAAK,CAACG,GAAG,CAAM,GAAGN,WAAW,CAACO,MAAM,gDAAgDF,YAAY,aAAaQ,UAAU,EAAE,CAAC;EAC1I;EAEAC,oBAAoB,CAACJ,QAAQ;IACzB,OAAO,IAAI,CAACP,KAAK,CAACY,GAAG,CAAM,GAAGf,WAAW,CAACO,MAAM,gCAAgC,EAAEG,QAAQ,CAAC;EAC/F;EAEAM,cAAc,CAACX,YAAoB,EAAEQ,UAAkB;IACnD,OAAO,IAAI,CAACV,KAAK,CAACc,MAAM,CAAM,GAAGjB,WAAW,CAACO,MAAM,gDAAgDF,YAAY,aAAaQ,UAAU,EAAE,CAAC;EAC7I;EAEAK,eAAe,CAACC,UAA2B;IACvC,OAAO,IAAI,CAAChB,KAAK,CAACY,GAAG,CAAM,GAAGf,WAAW,CAACO,MAAM,qCAAqC,EAAE;MACnFa,YAAY,EAAED;KACjB,CAAC;EACN;EAEAE,mBAAmB,CAACC,UAOnB;IACG,OAAO,IAAI,CAACnB,KAAK,CAACY,GAAG,CAAC,GAAGf,WAAW,CAACO,MAAM,sCAAsC,EAAEe,UAAU,CAAC;EAClG;EAEAC,QAAQ,CAACC,SAAS;IACd,OAAO,IAAI,CAACrB,KAAK,CAACQ,IAAI,CAAM,GAAGX,WAAW,CAACO,MAAM,0BAA0B,EAAEiB,SAAS,CAAC;EAC3F;EAEAC,WAAW,CAACD,SAAS;IACjB,OAAO,IAAI,CAACrB,KAAK,CAACY,GAAG,CAAM,GAAGf,WAAW,CAACO,MAAM,6BAA6B,EAAEiB,SAAS,CAAC;EAC7F;EAEAE,kBAAkB,CAACF,SAAS;IACxB,OAAO,IAAI,CAACrB,KAAK,CAACY,GAAG,CAAC,GAAGf,WAAW,CAACO,MAAM,gCAAgC,EAAEiB,SAAS,CAAC;EAC3F;EAEAG,aAAa,CAACtB,YAAoB;IAC9B,OAAO,IAAI,CAACF,KAAK,CAACc,MAAM,CAAM,GAAGjB,WAAW,CAACO,MAAM,kBAAkBF,YAAY,iBAAiB,CAAC;EACvG;EAEAuB,8BAA8B,CAACvB,YAAoB;IAC/C,OAAO,IAAI,CAACF,KAAK,CAACY,GAAG,CAAM,GAAGf,WAAW,CAACO,MAAM,kBAAkBF,YAAY,yBAAyB,EAAE,EAAE,CAAC;EAChH;EAAC;qBAhEQJ,mBAAmB;EAAA;EAAA;WAAnBA,mBAAmB;IAAA4B,SAAnB5B,mBAAmB;IAAA6B,YAFhB;EAAM", "names": ["environment", "AutoScheduleService", "constructor", "_http", "getScheduleMatches", "tournamentId", "get", "apiUrl", "getListUnScheduledMatches", "scheduleTournament", "formData", "post", "getScheduleConfig", "locationId", "updateScheduleConfig", "put", "deleteSchedule", "delete", "unScheduleMatch", "timeSlotId", "time_slot_id", "updateLocationMatch", "updateData", "addBreak", "formModel", "updateBreak", "updateMatchReferee", "clearSchedule", "updateTournamentScheduleStatus", "factory", "providedIn"], "sourceRoot": "", "sources": ["D:\\Code\\Work\\ezactive-vn\\ezleague-core\\client\\src\\app\\services\\auto-schedule.service.ts"], "sourcesContent": ["import {Injectable} from '@angular/core';\r\nimport {HttpClient} from '@angular/common/http';\r\nimport {environment} from 'environments/environment';\r\n\r\n@Injectable({\r\n    providedIn: 'root'\r\n})\r\nexport class AutoScheduleService {\r\n\r\n    constructor(private _http: HttpClient) {\r\n    }\r\n\r\n    getScheduleMatches(tournamentId: string) {\r\n        return this._http.get<any>(`${environment.apiUrl}/auto-schedule/${tournamentId}`);\r\n    }\r\n\r\n    getListUnScheduledMatches(tournamentId: string) {\r\n        return this._http.get<any>(`${environment.apiUrl}/auto-schedule/${tournamentId}/unscheduled-matches`);\r\n    }\r\n\r\n    scheduleTournament(formData) {\r\n        return this._http.post<any>(`${environment.apiUrl}/auto-schedule/generate`, formData);\r\n    }\r\n\r\n    getScheduleConfig(tournamentId: string, locationId: string) {\r\n        return this._http.get<any>(`${environment.apiUrl}/auto-schedule/schedule-config?tournament_id=${tournamentId}&location=${locationId}`);\r\n    }\r\n\r\n    updateScheduleConfig(formData) {\r\n        return this._http.put<any>(`${environment.apiUrl}/auto-schedule/schedule-config`, formData);\r\n    }\r\n\r\n    deleteSchedule(tournamentId: string, locationId: string) {\r\n        return this._http.delete<any>(`${environment.apiUrl}/auto-schedule/delete-schedule?tournament_id=${tournamentId}&location=${locationId}`);\r\n    }\r\n\r\n    unScheduleMatch(timeSlotId: string | number) {\r\n        return this._http.put<any>(`${environment.apiUrl}/auto-schedule/unschedule-time-slot`, {\r\n            time_slot_id: timeSlotId\r\n        });\r\n    }\r\n\r\n    updateLocationMatch(updateData: {\r\n        new_index: { [key: string | number]: number };\r\n        location_id: number;\r\n        prev_location_id: number;\r\n        stage_id: number;\r\n        prev_stage_id: number;\r\n        tournament_id: number;\r\n    }) {\r\n        return this._http.put(`${environment.apiUrl}/auto-schedule/update-location-match`, updateData);\r\n    }\r\n\r\n    addBreak(formModel) {\r\n        return this._http.post<any>(`${environment.apiUrl}/auto-schedule/add-break`, formModel);\r\n    }\r\n\r\n    updateBreak(formModel) {\r\n        return this._http.put<any>(`${environment.apiUrl}/auto-schedule/update-break`, formModel);\r\n    }\r\n\r\n    updateMatchReferee(formModel) {\r\n        return this._http.put(`${environment.apiUrl}/auto-schedule/update-referees`, formModel);\r\n    }\r\n\r\n    clearSchedule(tournamentId: string) {\r\n        return this._http.delete<any>(`${environment.apiUrl}/auto-schedule/${tournamentId}/clear-schedule`);\r\n    }\r\n\r\n    updateTournamentScheduleStatus(tournamentId: string) {\r\n        return this._http.put<any>(`${environment.apiUrl}/auto-schedule/${tournamentId}/update-schedule-status`, {});\r\n    }\r\n}\r\n"]}, "metadata": {}, "sourceType": "module", "externalDependencies": []}