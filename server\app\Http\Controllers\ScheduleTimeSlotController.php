<?php
namespace App\Http\Controllers;

use App\Models\ScheduleBreak;
use App\Models\ScheduleConfig;
use App\Models\ScheduleMatch;
use App\Models\ScheduleTimeSlot;
use App\Models\SeasonReferee;
use App\Models\Stage;
use App\Models\StageMatch;
use Carbon\Carbon;
use DateTime;
use DateTimeZone;
use Exception;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class ScheduleTimeSlotController extends Controller
{

    public function getScheduleTimeSlots($tournament_id)
    {
        try {

            $tournamentScheduleConfigs = ScheduleConfig::where('tournament_id', $tournament_id)
                ->with('tournament', 'location')
                ->get();

            $scheduleTimeSlots = ScheduleTimeSlot::where('tournament_id', $tournament_id)
                ->with(['tournament', 'location', 'stage', 'scheduleMatch', 'break', 'scheduleMatch.match.homeTeam', 'scheduleMatch.match.awayTeam'])
                ->whereNotNull('location_id')
                ->whereNotNull('start_time')
                ->whereNotNull('end_time')
                ->orderBy('slot_index', 'asc')
                ->orderBy('type', 'desc')
                ->orderBy('stage_id', 'asc')
                ->orderBy('location_id', 'asc')
                ->get();

            // Collect all unique referee IDs from schedule matches to fetch referee details
            $allRefereeIds = collect();
            foreach ($scheduleTimeSlots as $timeSlot) {
                if ($timeSlot->scheduleMatch && $timeSlot->scheduleMatch->referee_ids) {
                    $allRefereeIds = $allRefereeIds->merge($timeSlot->scheduleMatch->referee_ids);
                }
            }

            $allRefereeIds = $allRefereeIds->unique()->filter()->values();

            // Fetch all referee details
            $refereeDetails = [];
            if ($allRefereeIds->isNotEmpty()) {
                $referees = SeasonReferee::whereIn('id', $allRefereeIds)->with('user')->get();
                $refereeDetails = $referees->keyBy('id')->toArray();
            }

            $groupedMatches = [];

            $metadata = [
                'locations' => [],
                'configs' => [],
            ];

            $emptyLocation = collect();
            $listDates = collect();

            $timezone = request()->header('X-Time-Zone');

            // Build metadata for all locations first
            foreach ($tournamentScheduleConfigs as $config) {
                $metadata['configs'][] = array_merge($config->toArray(), [
                    "begin_date" => Carbon::parse($config->begin_date->format('Y-m-d') . " " . $config->begin_time->format('H:i:s'), 'UTC')->setTimezone($timezone)->format('Y-m-d'),
                ]);

                $locationName = $config->location ? $config->location->name : 'Unknown Location';

                if ($locationName !== 'Unknown Location' && !isset($metadata['locations'][$locationName])) {
                    $metadata['locations'][$locationName] = array_merge($config->location->toArray(), [
                        "begin_time" => $config->begin_time,
                    ]);
                }

                // Track expected dates from configs (for empty location handling later)
                $configDate = Carbon::parse($config->begin_date->format('Y-m-d') . " " . $config->begin_time->format('H:i:s'), 'UTC')->setTimezone($timezone)->format('Y-m-d');
                if (!$listDates->contains($configDate)) {
                    $listDates->push($configDate);
                }
                if (!isset($groupedMatches[$configDate])) {
                    $groupedMatches[$configDate] = [];
                }
            }

            // Process ALL time slots at once, without restrictive date filtering
            foreach ($scheduleTimeSlots as $timeSlot) {
                if (!$timeSlot->location || !$timeSlot->start_time || !$timeSlot->end_time) {
                    continue;
                }

                $locationName = $timeSlot->location->name;

                // Convert UTC times to local timezone to determine the correct date group
                $localStartTime = Carbon::parse($timeSlot->start_time, 'UTC')->setTimezone($timezone);
                $localDate = $localStartTime->format('Y-m-d');

                // Ensure the date is tracked in our list of dates
                if (!$listDates->contains($localDate)) {
                    $listDates->push($localDate);
                }

                // init object if not exist - use the timezone-converted date
                if (!isset($groupedMatches[$localDate])) {
                    $groupedMatches[$localDate] = [];
                }
                if (!isset($groupedMatches[$localDate][$locationName])) {
                    $groupedMatches[$localDate][$locationName] = [];
                }

                $timeSlotData = [
                    'type' => $timeSlot->type,
                    'start_time' => $timeSlot->start_time,
                    'end_time' => $timeSlot->end_time,
                ];

                $scheduleData = ($timeSlot->scheduleMatch ?? $timeSlot->break)?->toArray() ?? [];

                if ($timeSlot->scheduleMatch && $timeSlot->scheduleMatch->referee_ids) {
                    $matchReferees = [];
                    foreach ($timeSlot->scheduleMatch->referee_ids as $refereeId) {
                        if (isset($refereeDetails[$refereeId])) {
                            $matchReferees[] = $refereeDetails[$refereeId];
                        }
                    }
                    $scheduleData['referees'] = $matchReferees;
                } else {
                    $scheduleData['referees'] = [];
                }

                $groupedMatches[$localDate][$locationName][] = array_merge($timeSlotData, $scheduleData);
            }

            // Handle empty locations - find locations that have configs but no matches
            $configuredLocations = $tournamentScheduleConfigs->pluck('location.name')->filter()->unique();
            $locationsWithMatches = collect($groupedMatches)->flatMap(function ($dateGroup) {
                return array_keys($dateGroup);
            })->unique();

            $emptyLocation = $configuredLocations->diff($locationsWithMatches);

            // Handle empty locations - ensure they have entries for dates they're configured for
            $emptyLocation->each(function ($locationName) use ($listDates, &$groupedMatches, $tournamentScheduleConfigs, $timezone) {
                $listDates->each(function ($date) use ($locationName, &$groupedMatches, $tournamentScheduleConfigs, $timezone) {
                    // Check if this location has a config for this date
                    $hasConfig = $tournamentScheduleConfigs->filter(function ($config) use ($locationName, $date, $timezone) {
                        if (!$config->location || $config->location->name !== $locationName) {
                            return false;
                        }

                        // Convert config date to timezone for comparison
                        $configDate = Carbon::parse($config->begin_date->format('Y-m-d') . " " . $config->begin_time->format('H:i:s'), 'UTC')
                            ->setTimezone($timezone)
                            ->format('Y-m-d');

                        return $configDate === $date;
                    })->count();

                    // If location has config for this date but no matches, create empty array
                    if ($hasConfig > 0 && !isset($groupedMatches[$date][$locationName])) {
                        $groupedMatches[$date][$locationName] = [];
                    }
                });
            });

            $conflicts = (new ScheduleMatchController())->getScheduleConflict($tournament_id);

            return response()->json([
                'data' => $groupedMatches,
                'metadata' => $metadata,
                'conflicts' => $conflicts,
            ]);
        } catch (Exception $error) {
            Log::error("Error fetching schedule time slots: " . $error->getMessage());
            return [];
        }
    }

    public function getUnScheduledMatches($tournament_id)
    {
        try {
            $unscheduledMatches = ScheduleTimeSlot::where('tournament_id', $tournament_id)
                ->with(['tournament', 'scheduleMatch.match', 'scheduleMatch.match.homeTeam', 'scheduleMatch.match.awayTeam'])
                ->whereNull('location_id')
                ->whereNull('start_time')
                ->whereNull('end_time')
                ->get()->toArray();

            foreach ($unscheduledMatches as &$match) {
                $match['match'] = $match['schedule_match']['match'] ?? null;
                $match['time_slot_id'] = $match['id'];
            }

            return response()->json([
                'data' => $unscheduledMatches,
            ]);
        } catch (Exception $error) {
            Log::error("Error fetching unscheduled matches: " . $error->getMessage());
            return response()->json([
                'message' => 'Error fetching unscheduled matches: ' . $error->getMessage(),
            ], 500);
        }
    }

    public function deleteAllTimeSlotInTournament($tournamentId, $listMatchIds = [])
    {
        try {
            if (empty($listMatchIds)) {
                // delete all if $listMatchIds is empty
                ScheduleTimeSlot::where('tournament_id', $tournamentId)->delete();
            } else {
                // only delete ids in $listMatchIds
                ScheduleTimeSlot::where('tournament_id', $tournamentId)
                    ->whereHas('scheduleMatch', function ($query) use ($listMatchIds) {
                        $query->whereIn('match_id', $listMatchIds);
                    })
                    ->delete();
            }
            return true;
        } catch (Exception $error) {
            Log::error("Error clearing time slots: " . $error->getMessage());
            return false;
        }
    }

    public function clearScheduleOfTournament($tournament_id)
    {
        try {

            ScheduleTimeSlot::where('tournament_id', $tournament_id)->update([
                'location_id' => null,
                'start_time' => null,
                'end_time' => null,
            ]);

            ScheduleConfig::where('tournament_id', $tournament_id)->delete();

            (new ScheduleMatchController())->submitSchedule($tournament_id);

            return response()->json([
                'status' => 'success',
                'message' => 'Schedule cleared successfully',
            ]);
        } catch (Exception $error) {
            Log::error('error', [$error]);
            return response()->json([
                'message' => 'Error clearing schedule: ' . $error->getMessage(),
            ]);
        }
    }

    public function reIndexSlot($tournamentId, $locationId)
    {
        // Get all unique dates for the time slots
        $dates = ScheduleTimeSlot::where('tournament_id', $tournamentId)
            ->where('location_id', $locationId)
            ->whereNotNull('start_time')
            ->select(DB::raw('DATE(start_time) as date'))
            ->distinct()
            ->orderBy('date')
            ->pluck('date');

        // For each date, get time slots and reindex them
        foreach ($dates as $date) {
            ScheduleTimeSlot::where('tournament_id', $tournamentId)
                ->where('location_id', $locationId)
                ->whereDate('start_time', $date)
                ->orderBy('start_time')
                ->get()
                ->each(function ($timeSlot, $index) {
                    $timeSlot->slot_index = $index;
                    $timeSlot->save();
                });
        }
    }

    public function generateTimeSlots($tournamentId, $locationId, $stageId, $beginDate, $beginTime, $endTime, $matchDuration, $breakDuration, $repeats, $timeZone, $foreUpdateConfig = false)
    {
        try {

            $scheduleConfigController = new ScheduleConfigController();
            // get last slot index of location in stage
            $lastSlotIndex = ScheduleTimeSlot::where('stage_id', $stageId)
                ->where('location_id', $locationId)
                ->orderBy('slot_index', 'desc')
                ->first()->slot_index ?? null;

            $results = [];

            $localTimezone = new DateTimeZone($timeZone);
            $utcTimezone = new DateTimeZone('UTC');
            $baseStartTime = new DateTime("$beginDate $beginTime", $localTimezone);
            $baseEndTime = new DateTime("$beginDate " . ($endTime ?: '23:59:59'), $localTimezone);

            $scheduleConfigController->updateOrCreateConfig(
                $tournamentId,
                $locationId,
                $baseStartTime->format('Y-m-d'),
                $baseStartTime->format('H:i:s'),
                $baseEndTime->format('H:i:s'),
                $matchDuration,
                $breakDuration,
                $timeZone,
                $foreUpdateConfig
            );

            $start = clone $baseStartTime;

            for ($i = 0; $i < $repeats; $i++) {

                $end = clone $start;
                $end->modify("+$matchDuration minutes");

                $startUTC = clone $start;
                $startUTC->setTimezone($utcTimezone);

                $endUTC = clone $end;
                $endUTC->setTimezone($utcTimezone);

                // Always store times in UTC for consistency
                $results[] = [
                    'tournament_id' => $tournamentId,
                    'location_id' => $locationId,
                    'stage_id' => $stageId,
                    'start_time' => $startUTC->format('Y-m-d H:i:s'),
                    'end_time' => $endUTC->format('Y-m-d H:i:s'),
                    'slot_index' => $lastSlotIndex ? $i + $lastSlotIndex + 1 : $i,
                ];

                $start = clone $end;

                if (!$this->checkNextMatchTime(clone $start, $baseEndTime, $matchDuration, $breakDuration)) {
                    $baseStartTime->modify("+1 day");
                    $baseEndTime->modify("+1 day");
                    $start = clone $baseStartTime;
                    $scheduleConfigController->updateOrCreateConfig($tournamentId, $locationId, $baseStartTime->format('Y-m-d'), $baseStartTime->format('H:i:s'), $baseEndTime->format('H:i:s'), $matchDuration, $breakDuration, $timeZone, true);
                } else {
                    $start->modify("+$breakDuration minutes");
                }

            }

            $modifyIds = $this->createOrUpdateTimeSlot($results);

            $this->reIndexSlot($tournamentId, $locationId);

            return $modifyIds;
        } catch (Exception $error) {
            Log::error("Error generating time slots: " . $error->getMessage());
            return [];
        }
    }

    public function checkNextMatchTime(DateTime $startTime, DateTime $endTimeOfDate, $matchDuration, $breakDuration)
    {

        if ($startTime->modify("+$matchDuration minutes") > $endTimeOfDate) {
            return false;
        }

        return true;

    }

    public function createOrUpdateTimeSlot($timeslotData)
    {
        $modifyIds = [];

        foreach ($timeslotData as $timeSlot) {

            // update by time slot id
            if (isset($timeSlot['id'])) {
                $modifyIds[] = ScheduleTimeSlot::where('id', $timeSlot['id'])
                    ->update([
                        'location_id' => $timeSlot['location_id'],
                        'stage_id' => $timeSlot['stage_id'],
                        'start_time' => $timeSlot['start_time'],
                        'end_time' => $timeSlot['end_time'],
                        'slot_index' => $timeSlot['slot_index'],
                    ]);
            } else {

                // check if time slot exist
                $isExist = ScheduleTimeSlot::where('tournament_id', $timeSlot['tournament_id'])
                    ->where('location_id', $timeSlot['location_id'])
                    ->where('stage_id', $timeSlot['stage_id'])
                    ->where('slot_index', $timeSlot['slot_index'])
                    ->exists();

                if (!$isExist) {
                    // create new if not exists
                    $modifyIds[] = ScheduleTimeSlot::create([
                        'tournament_id' => $timeSlot['tournament_id'],
                        'location_id' => $timeSlot['location_id'],
                        'stage_id' => $timeSlot['stage_id'],
                        'start_time' => $timeSlot['start_time'],
                        'end_time' => $timeSlot['end_time'],
                        'slot_index' => $timeSlot['slot_index'],
                    ]);
                } else {
                    // update if exists
                    $modifyIds[] = ScheduleTimeSlot::where('tournament_id', $timeSlot['tournament_id'])
                        ->where('location_id', $timeSlot['location_id'])
                        ->where('stage_id', $timeSlot['stage_id'])
                        ->where('slot_index', $timeSlot['slot_index'])
                        ->update([
                            'start_time' => $timeSlot['start_time'],
                            'end_time' => $timeSlot['end_time'],
                        ]);
                }
            }
        }

        return $modifyIds;
    }

    public function getTimeSlots($tournamentId, $locationId)
    {
        return ScheduleTimeSlot::where('tournament_id', $tournamentId)
            ->where('location_id', $locationId)
            ->orderBy('start_time')
            ->get();
    }

    // regenerate by exists config method
    public function regenerateTimeSlots($tournamentId, $locationId, $date, $newConfigBeginTime = null)
    {
        try {

            $config = ScheduleConfig::where([
                'tournament_id' => $tournamentId,
                'location_id' => $locationId,
                'begin_date' => $date,
            ]);

            if ($newConfigBeginTime) {
                $config->where('begin_time', $newConfigBeginTime);
            }

            $config = $config->first();

            Log::info('$config', [$config]);

            if (!$config) {
                throw new Exception('Schedule configuration not found');
            }

            $configBeginDate = Carbon::parse($config->begin_date, 'UTC')->format('Y-m-d');
            $configBeginTime = Carbon::parse($config->begin_time, 'UTC')->format('H:i:s');

            $listTimeSlots = ScheduleTimeSlot::where([
                'tournament_id' => $tournamentId,
                'location_id' => $locationId,

            ])
                ->where(function ($query) use ($config, $configBeginDate) {

                    $startOfDay = Carbon::parse($configBeginDate . ' 00:00:00', 'UTC');
                    $endOfDay = Carbon::parse($configBeginDate . ' 23:59:59', 'UTC');

                    $query->where('start_time', '>=', $startOfDay)
                        ->where('end_time', '<=', $endOfDay);
                })
                ->with(['tournament', 'location', 'stage', 'scheduleMatch', 'break'])
                ->orderBy('slot_index', 'asc')
                ->orderBy('type', 'desc')
                ->get();

            // Config times are now stored in UTC, so parse them correctly
            $beginDateTime = $configBeginDate . ' ' . $configBeginTime;
            $beginDateTimeUTC = Carbon::parse($beginDateTime, 'UTC');

            $nextMatchTime = [
                'start_time' => $beginDateTimeUTC->copy(),
                'end_time' => $beginDateTimeUTC->copy()->addMinutes($config->match_duration),
            ];

            foreach ($listTimeSlots as $index => $timeSlot) {

                $timeSlot->slot_index = $index;

                if ($timeSlot->type == 'break') {
                    $startTime = $nextMatchTime['start_time']->copy();
                    $endTime = $startTime->copy()->addMinutes($timeSlot->break->break_durations);

                    // Store as UTC formatted strings
                    $timeSlot->start_time = $startTime->utc()->format('Y-m-d H:i:s');
                    $timeSlot->end_time = $endTime->utc()->format('Y-m-d H:i:s');

                    $nextMatchTime = [
                        'start_time' => $endTime->copy(),
                        'end_time' => $endTime->copy()->addMinutes($config->match_duration),
                    ];
                } else {
                    $startTime = $nextMatchTime['start_time']->copy();
                    $endTime = $nextMatchTime['end_time']->copy();

                    // Store as UTC formatted strings
                    $timeSlot->start_time = $startTime->utc()->format('Y-m-d H:i:s');
                    $timeSlot->end_time = $endTime->utc()->format('Y-m-d H:i:s');

                    $newStartTime = $endTime->copy()->addMinutes($config->break_match_duration);
                    $newEndTime = $newStartTime->copy()->addMinutes($config->match_duration);
                    $nextMatchTime = [
                        'start_time' => $newStartTime,
                        'end_time' => $newEndTime,
                    ];
                }
                $timeSlot->save();
            }

            (new ScheduleMatchController())->submitSchedule($tournamentId);

            return $this->getTimeSlots($tournamentId, $locationId);
        } catch (Exception $error) {
            Log::error("Error regenerating time slots: " . $error->getMessage());
            throw new Exception('Error regenerating time slots: ' . $error->getMessage());
        }
    }

    public function addBreakTimeToLocation($tournamentId, $locationId, $breakDurations)
    {
        // get last match of location in stage
        $lastMatch = ScheduleTimeSlot::where('tournament_id', $tournamentId)
            ->where('location_id', $locationId)
            ->orderBy('slot_index', 'desc')->first();

        // create new time slot with type `break`
        $newTimeSlot = ScheduleTimeSlot::create([
            'tournament_id' => $tournamentId,
            'location_id' => $locationId,
            'start_time' => $lastMatch->end_time,
            'end_time' => date('Y-m-d H:i:s', strtotime($lastMatch->end_time) + ($breakDurations * 60)),
            'type' => 'break',
            'slot_index' => $lastMatch->slot_index + 1,
        ]);

        return $newTimeSlot->id;
    }

    public function deleteSchedule(Request $request)
    {
        try {
            $request->validate([
                'tournament_id' => 'required|integer|exists:tournaments,id',
                'location' => 'required|string',
                'date' => 'required|date_format:Y-m-d',
            ]);

            $tournamentId = $request->get('tournament_id');
            $locationId = $request->get('location');
            $date = $request->get('date');

            // update if type match
            ScheduleTimeSlot::where([
                'tournament_id' => $tournamentId,
                'location_id' => $locationId,
                'type' => 'match',
            ])
                ->where(function ($query) use ($date) {
                    $startOfDay = Carbon::parse($date . ' 00:00:00', 'UTC');
                    $endOfDay = Carbon::parse($date . ' 23:59:59', 'UTC');

                    $query->where('start_time', '>=', $startOfDay)
                        ->where('end_time', '<=', $endOfDay);
                })
                ->update([
                    'location_id' => null,
                    'start_time' => null,
                    'end_time' => null,
                ]);

            ScheduleTimeSlot::where([
                'tournament_id' => $tournamentId,
                'location_id' => $locationId,
                'type' => 'break',
            ])->where(function ($query) use ($date) {
                $startOfDay = Carbon::parse($date . ' 00:00:00', 'UTC');
                $endOfDay = Carbon::parse($date . ' 23:59:59', 'UTC');

                $query->where('start_time', '>=', $startOfDay)
                    ->where('end_time', '<=', $endOfDay);
            })
                ->delete();

            // delete schedule config
            ScheduleConfig::where([
                'tournament_id' => $tournamentId,
                'location_id' => $locationId,
                'begin_date' => $date,
            ])->delete();

            (new ScheduleMatchController())->submitSchedule($tournamentId);

            return response()->json([
                'message' => 'Deleted plan successfully.',
            ]);
        } catch (Exception $error) {
            Log::error("Error deleting plan:", [$error]);
            return response()->json([
                'message' => 'Error deleting plan: ' . $error->getMessage(),
            ], 500);
        }
    }

    public function unScheduleTimeSlot(Request $request)
    {
        // validate time_slot_id in request
        try {
            $request->validate([
                'time_slot_id' => 'required|integer|exists:schedule_time_slots,id',
                'date' => 'required|date_format:Y-m-d',
            ]);

            $currentTimeSlot = ScheduleTimeSlot::find($request->get('time_slot_id'));

            $locationId = $currentTimeSlot->location_id;
            $tournamentId = $currentTimeSlot->tournament_id;
            $type = $currentTimeSlot->type;

            if ($type == 'break') {
                ScheduleBreak::where('time_slot_id', $request->get('time_slot_id'))->delete();
                $currentTimeSlot->delete();
            } else {
                $currentTimeSlot->update([
                    'location_id' => null,
                    'start_time' => null,
                    'end_time' => null,
                ]);
            }
            $this->regenerateTimeSlots($tournamentId, $locationId, $request->get('date'));

            return response()->json([
                'message' => 'Unscheduled time slot successfully.',
            ]);
        } catch (Exception $error) {
            Log::error("Error unscheduling match:", [$error]);
            return response()->json([
                'message' => $error->getMessage(),
            ], 500);
        }
    }

    // update schedule time by list new index
    public function updateLocationMatch(Request $request)
    {
        try {

            $request->validate([
                'new_index' => 'required|array',
                'location_id' => 'required|integer',
                'prev_location_id' => 'sometimes|integer|nullable',
                'tournament_id' => 'required|integer',
                'date' => 'required|date_format:Y-m-d',
            ]);

            // update new index
            foreach ($request->get('new_index') as $timeSlotId => $index) {
                ScheduleTimeSlot::find($timeSlotId)->update([
                    'location_id' => $request->get('location_id'),
                    'slot_index' => $index,
                ]);
            }

            // regenerate schedule time
            $this->regenerateTimeSlots(
                $request->get('tournament_id'),
                $request->get('location_id'),
                $request->get('date')
            );

            // if prev location not same as current location, regenerate schedule time for prev location
            if ($request->get('prev_location_id') != null) {
                $this->regenerateTimeSlots(
                    $request->get('tournament_id'),
                    $request->get('prev_location_id'),
                    $request->get('date')
                );
            }

            return response()->json([
                'message' => 'Updated match schedule successfully.',
            ]);
        } catch (Exception $error) {
            Log::error("Error updating match schedule:", [$error]);
            return response()->json([
                'message' => $error->getMessage(),
            ], 500);
        }
    }

    public function autoInitTimeSlotByStageId($stageId)
    {
        try {
            $stageInfo = Stage::find($stageId);
            $tournamentId = $stageInfo->tournament->id;
            $stageMatches = StageMatch::where('stage_id', $stageId)->get();

            foreach ($stageMatches as $match) {
                $timeSlot = ScheduleTimeSlot::create([
                    'tournament_id' => $tournamentId,
                    'stage_id' => $stageId,
                    'match_id' => $match->id,
                    'location_id' => null,
                    'start_time' => null,
                    'end_time' => null,
                ]);
                ScheduleMatch::create([
                    'tournament_id' => $tournamentId,
                    'stage_id' => $stageId,
                    'match_id' => $match->id,
                    'time_slot_id' => $timeSlot->id,
                ]);
            }
        } catch (Exception $error) {
            Log::info('error', [$error]);
            throw new Exception("Something went wrong");
        }
    }
}
