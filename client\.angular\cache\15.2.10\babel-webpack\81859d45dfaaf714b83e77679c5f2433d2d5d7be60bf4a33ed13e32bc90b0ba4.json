{"ast": null, "code": "import { EventEmitter } from '@angular/core';\nimport { FormGroup } from '@angular/forms';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@ng-bootstrap/ng-bootstrap\";\nimport * as i2 from \"@ngx-translate/core\";\nimport * as i3 from \"../../../../services/auto-schedule.service\";\nimport * as i4 from \"app/services/loading.service\";\nimport * as i5 from \"ngx-toastr\";\nimport * as i6 from \"@angular/forms\";\nimport * as i7 from \"@core/directives/core-ripple-effect/core-ripple-effect.directive\";\nimport * as i8 from \"@ngx-formly/core\";\nexport class ModalUpdateConfigComponent {\n  constructor(_modalService, _translateService, _autoSchedule, _loadingService, _toastService) {\n    this._modalService = _modalService;\n    this._translateService = _translateService;\n    this._autoSchedule = _autoSchedule;\n    this._loadingService = _loadingService;\n    this._toastService = _toastService;\n    this.selectedConfig = null;\n    this.editForm = new FormGroup({});\n    this.editModel = {};\n    this.editFields = [{\n      key: 'begin_date',\n      type: 'input',\n      props: {\n        label: this._translateService.instant('Date'),\n        placeholder: this._translateService.instant('Enter begin date'),\n        required: true,\n        type: 'date'\n      }\n    }, {\n      key: 'begin_time',\n      type: 'input',\n      props: {\n        label: this._translateService.instant('Begin time'),\n        placeholder: this._translateService.instant('Enter begin time'),\n        required: true,\n        type: 'time'\n      },\n      validation: {\n        messages: {\n          required: this._translateService.instant('Begin time is required.')\n        }\n      }\n    }, {\n      key: 'old_begin_time',\n      type: 'input',\n      props: {\n        type: 'hidden'\n      }\n    }, {\n      key: 'match_duration',\n      type: 'input',\n      props: {\n        label: this._translateService.instant('Match duration'),\n        placeholder: this._translateService.instant('Enter match duration (in minutes)'),\n        required: true,\n        type: 'number',\n        min: 1\n      },\n      validation: {\n        messages: {\n          required: this._translateService.instant('Match duration is required.'),\n          min: this._translateService.instant('Match duration must be at least 1 minute.')\n        }\n      }\n    }, {\n      key: 'break_duration',\n      type: 'input',\n      props: {\n        label: this._translateService.instant('Break duration'),\n        placeholder: this._translateService.instant('Enter break between match duration (in minutes)'),\n        required: true,\n        type: 'number',\n        min: 0\n      },\n      validation: {\n        messages: {\n          required: this._translateService.instant('Break duration is required.'),\n          min: this._translateService.instant('Break duration must be at least 0 minutes.')\n        }\n      }\n    }, {\n      key: 'location_id',\n      type: 'input',\n      props: {\n        type: 'hidden'\n      }\n    }, {\n      key: 'tournament_id',\n      type: 'input',\n      props: {\n        type: 'hidden'\n      }\n    }, {\n      key: 'date',\n      type: 'input',\n      props: {\n        type: 'hidden'\n      }\n    }];\n    this.onSubmit = new EventEmitter();\n  }\n  ngOnInit() {\n    if (!this.selectedConfig) {\n      console.error('No selected configuration provided');\n      return;\n    }\n    this._loadingService.show();\n    this._autoSchedule.getScheduleConfigById(this.selectedConfig.configId).subscribe(res => {\n      console.log('🚀 ~ ngOnInit ~ res: ', res);\n      // Convert ISO date string to 'YYYY-MM-DD' for input[type=\"date\"]\n      const beginDate = res.data.begin_date ? new Date(res.data.begin_date).toISOString().slice(0, 10) : '';\n      this.editModel = {\n        ...this.editModel,\n        begin_date: beginDate,\n        begin_time: res.data.begin_time,\n        old_begin_time: `${beginDate} ${res.data.begin_time}`,\n        match_duration: res.data.match_duration,\n        break_duration: res.data.break_match_duration,\n        tournament_id: res.data.tournament_id,\n        location_id: res.data.location_id,\n        date: this.selectedConfig.date\n      };\n      console.log(\"🚀 ~ ModalUpdateConfigComponent ~ .subscribe ~ editModel:\", this.editModel);\n      this._loadingService.dismiss();\n    });\n  }\n  onSubmitEdit(model) {\n    console.log('🚀 ~ onSubmitEdit ~ model: ', model);\n    this._autoSchedule.updateScheduleConfig(model).subscribe(res => {\n      this._toastService.success(this._translateService.instant('Schedule configuration updated successfully.'));\n      this.onSubmit.emit(res);\n      this._modalService.dismissAll();\n    }, error => {\n      console.error('Error scheduling tournament:', error);\n    });\n  }\n  closeModal() {\n    this.editModel = {};\n    this._modalService.dismissAll();\n  }\n  clearForm() {\n    this.editForm.reset();\n  }\n  static #_ = this.ɵfac = function ModalUpdateConfigComponent_Factory(t) {\n    return new (t || ModalUpdateConfigComponent)(i0.ɵɵdirectiveInject(i1.NgbModal), i0.ɵɵdirectiveInject(i2.TranslateService), i0.ɵɵdirectiveInject(i3.AutoScheduleService), i0.ɵɵdirectiveInject(i4.LoadingService), i0.ɵɵdirectiveInject(i5.ToastrService));\n  };\n  static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: ModalUpdateConfigComponent,\n    selectors: [[\"app-modal-update-config\"]],\n    inputs: {\n      selectedConfig: \"selectedConfig\"\n    },\n    outputs: {\n      onSubmit: \"onSubmit\"\n    },\n    decls: 14,\n    vars: 11,\n    consts: [[1, \"modal-header\"], [\"id\", \"editScheduleConfig\", 1, \"modal-title\"], [\"type\", \"button\", \"aria-label\", \"Close\", 1, \"close\", 3, \"click\"], [\"aria-hidden\", \"true\"], [3, \"formGroup\", \"ngSubmit\"], [\"tabindex\", \"0\", \"ngbAutofocus\", \"\", 1, \"modal-body\"], [3, \"form\", \"fields\", \"model\", \"submit\"], [1, \"modal-footer\"], [\"type\", \"submit\", \"rippleEffect\", \"\", 1, \"w-100\", \"btn\", \"btn-primary\", 3, \"disabled\"]],\n    template: function ModalUpdateConfigComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵelementStart(0, \"div\", 0)(1, \"h5\", 1);\n        i0.ɵɵtext(2);\n        i0.ɵɵpipe(3, \"translate\");\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(4, \"button\", 2);\n        i0.ɵɵlistener(\"click\", function ModalUpdateConfigComponent_Template_button_click_4_listener() {\n          return ctx.closeModal();\n        });\n        i0.ɵɵelementStart(5, \"span\", 3);\n        i0.ɵɵtext(6, \"\\u00D7\");\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelementStart(7, \"form\", 4);\n        i0.ɵɵlistener(\"ngSubmit\", function ModalUpdateConfigComponent_Template_form_ngSubmit_7_listener() {\n          return ctx.onSubmitEdit(ctx.editModel);\n        });\n        i0.ɵɵelementStart(8, \"div\", 5)(9, \"formly-form\", 6);\n        i0.ɵɵlistener(\"submit\", function ModalUpdateConfigComponent_Template_formly_form_submit_9_listener() {\n          return ctx.onSubmitEdit(ctx.editModel);\n        });\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(10, \"div\", 7)(11, \"button\", 8);\n        i0.ɵɵtext(12);\n        i0.ɵɵpipe(13, \"translate\");\n        i0.ɵɵelementEnd()()();\n      }\n      if (rf & 2) {\n        i0.ɵɵadvance(2);\n        i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(3, 7, \"Edit Schedule Config\"));\n        i0.ɵɵadvance(5);\n        i0.ɵɵproperty(\"formGroup\", ctx.editForm);\n        i0.ɵɵadvance(2);\n        i0.ɵɵproperty(\"form\", ctx.editForm)(\"fields\", ctx.editFields)(\"model\", ctx.editModel);\n        i0.ɵɵadvance(2);\n        i0.ɵɵproperty(\"disabled\", ctx.editForm.invalid);\n        i0.ɵɵadvance(1);\n        i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(13, 9, \"Re-schedule Match\"), \" \");\n      }\n    },\n    dependencies: [i6.ɵNgNoValidate, i6.NgControlStatusGroup, i6.FormGroupDirective, i7.RippleEffectDirective, i8.FormlyForm, i2.TranslatePipe],\n    styles: [\"\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IiIsInNvdXJjZVJvb3QiOiIifQ== */\"]\n  });\n}", "map": {"version": 3, "mappings": "AAAA,SAAoBA,YAAY,QAA+B,eAAe;AAC9E,SAASC,SAAS,QAAQ,gBAAgB;;;;;;;;;;AAoB1C,OAAM,MAAOC,0BAA0B;EAyGnCC,YACYC,aAAuB,EACvBC,iBAAmC,EACnCC,aAAkC,EAClCC,eAA+B,EAC/BC,aAA4B;IAJ5B,kBAAa,GAAbJ,aAAa;IACb,sBAAiB,GAAjBC,iBAAiB;IACjB,kBAAa,GAAbC,aAAa;IACb,oBAAe,GAAfC,eAAe;IACf,kBAAa,GAAbC,aAAa;IA5GhB,mBAAc,GAA8B,IAAI;IAEzD,aAAQ,GAAG,IAAIP,SAAS,CAAC,EAAE,CAAC;IAC5B,cAAS,GAAG,EAAE;IAEP,eAAU,GAAwB,CACrC;MACIQ,GAAG,EAAE,YAAY;MACjBC,IAAI,EAAE,OAAO;MACbC,KAAK,EAAE;QACHC,KAAK,EAAE,IAAI,CAACP,iBAAiB,CAACQ,OAAO,CAAC,MAAM,CAAC;QAC7CC,WAAW,EAAE,IAAI,CAACT,iBAAiB,CAACQ,OAAO,CAAC,kBAAkB,CAAC;QAC/DE,QAAQ,EAAE,IAAI;QACdL,IAAI,EAAE;;KAEb,EACD;MACID,GAAG,EAAE,YAAY;MACjBC,IAAI,EAAE,OAAO;MACbC,KAAK,EAAE;QACHC,KAAK,EAAE,IAAI,CAACP,iBAAiB,CAACQ,OAAO,CAAC,YAAY,CAAC;QACnDC,WAAW,EAAE,IAAI,CAACT,iBAAiB,CAACQ,OAAO,CACvC,kBAAkB,CACrB;QACDE,QAAQ,EAAE,IAAI;QACdL,IAAI,EAAE;OACT;MACDM,UAAU,EAAE;QACRC,QAAQ,EAAE;UACNF,QAAQ,EAAE,IAAI,CAACV,iBAAiB,CAACQ,OAAO,CAAC,yBAAyB;;;KAG7E,EACD;MACIJ,GAAG,EAAE,gBAAgB;MACrBC,IAAI,EAAE,OAAO;MACbC,KAAK,EAAE;QACHD,IAAI,EAAE;;KAEb,EACD;MACID,GAAG,EAAE,gBAAgB;MACrBC,IAAI,EAAE,OAAO;MACbC,KAAK,EAAE;QACHC,KAAK,EAAE,IAAI,CAACP,iBAAiB,CAACQ,OAAO,CAAC,gBAAgB,CAAC;QACvDC,WAAW,EAAE,IAAI,CAACT,iBAAiB,CAACQ,OAAO,CACvC,mCAAmC,CACtC;QACDE,QAAQ,EAAE,IAAI;QACdL,IAAI,EAAE,QAAQ;QACdQ,GAAG,EAAE;OACR;MACDF,UAAU,EAAE;QACRC,QAAQ,EAAE;UACNF,QAAQ,EAAE,IAAI,CAACV,iBAAiB,CAACQ,OAAO,CAAC,6BAA6B,CAAC;UACvEK,GAAG,EAAE,IAAI,CAACb,iBAAiB,CAACQ,OAAO,CAAC,2CAA2C;;;KAG1F,EACD;MACIJ,GAAG,EAAE,gBAAgB;MACrBC,IAAI,EAAE,OAAO;MACbC,KAAK,EAAE;QACHC,KAAK,EAAE,IAAI,CAACP,iBAAiB,CAACQ,OAAO,CAAC,gBAAgB,CAAC;QACvDC,WAAW,EAAE,IAAI,CAACT,iBAAiB,CAACQ,OAAO,CACvC,iDAAiD,CACpD;QACDE,QAAQ,EAAE,IAAI;QACdL,IAAI,EAAE,QAAQ;QACdQ,GAAG,EAAE;OACR;MACDF,UAAU,EAAE;QACRC,QAAQ,EAAE;UACNF,QAAQ,EAAE,IAAI,CAACV,iBAAiB,CAACQ,OAAO,CAAC,6BAA6B,CAAC;UACvEK,GAAG,EAAE,IAAI,CAACb,iBAAiB,CAACQ,OAAO,CAAC,4CAA4C;;;KAG3F,EACD;MACIJ,GAAG,EAAE,aAAa;MAClBC,IAAI,EAAE,OAAO;MACbC,KAAK,EAAE;QACHD,IAAI,EAAE;;KAEb,EACD;MACID,GAAG,EAAE,eAAe;MACpBC,IAAI,EAAE,OAAO;MACbC,KAAK,EAAE;QACHD,IAAI,EAAE;;KAEb,EACD;MACID,GAAG,EAAE,MAAM;MACXC,IAAI,EAAE,OAAO;MACbC,KAAK,EAAE;QACHD,IAAI,EAAE;;KAEb,CACJ;IAES,aAAQ,GAAG,IAAIV,YAAY,EAAE;EAUvC;EAEAmB,QAAQ;IACJ,IAAI,CAAC,IAAI,CAACC,cAAc,EAAE;MACtBC,OAAO,CAACC,KAAK,CAAC,oCAAoC,CAAC;MACnD;;IAIJ,IAAI,CAACf,eAAe,CAACgB,IAAI,EAAE;IAE3B,IAAI,CAACjB,aAAa,CAACkB,qBAAqB,CAAC,IAAI,CAACJ,cAAc,CAACK,QAAQ,CAAC,CACjEC,SAAS,CAAEC,GAAG,IAAI;MACfN,OAAO,CAACO,GAAG,CAAC,uBAAuB,EAAED,GAAG,CAAC;MACzC;MACA,MAAME,SAAS,GAAGF,GAAG,CAACG,IAAI,CAACC,UAAU,GAC/B,IAAIC,IAAI,CAACL,GAAG,CAACG,IAAI,CAACC,UAAU,CAAC,CAACE,WAAW,EAAE,CAACC,KAAK,CAAC,CAAC,EAAE,EAAE,CAAC,GACxD,EAAE;MAER,IAAI,CAACC,SAAS,GAAG;QACb,GAAG,IAAI,CAACA,SAAS;QACjBJ,UAAU,EAAEF,SAAS;QACrBO,UAAU,EAAET,GAAG,CAACG,IAAI,CAACM,UAAU;QAC/BC,cAAc,EAAE,GAAGR,SAAS,IAAIF,GAAG,CAACG,IAAI,CAACM,UAAU,EAAE;QACrDE,cAAc,EAAEX,GAAG,CAACG,IAAI,CAACQ,cAAc;QACvCC,cAAc,EAAEZ,GAAG,CAACG,IAAI,CAACU,oBAAoB;QAC7CC,aAAa,EAAEd,GAAG,CAACG,IAAI,CAACW,aAAa;QACrCC,WAAW,EAAEf,GAAG,CAACG,IAAI,CAACY,WAAW;QACjCC,IAAI,EAAE,IAAI,CAACvB,cAAc,CAACuB;OAC7B;MAEDtB,OAAO,CAACO,GAAG,CAAC,2DAA2D,EAAE,IAAI,CAACO,SAAS,CAAC;MAIxF,IAAI,CAAC5B,eAAe,CAACqC,OAAO,EAAE;IAElC,CAAC,CAAC;EACV;EAEAC,YAAY,CAACC,KAAK;IACdzB,OAAO,CAACO,GAAG,CAAC,6BAA6B,EAAEkB,KAAK,CAAC;IACjD,IAAI,CAACxC,aAAa,CAACyC,oBAAoB,CAACD,KAAK,CAAC,CAACpB,SAAS,CAAEC,GAAG,IAAI;MAC7D,IAAI,CAACnB,aAAa,CAACwC,OAAO,CAAC,IAAI,CAAC3C,iBAAiB,CAACQ,OAAO,CAAC,8CAA8C,CAAC,CAAC;MAC1G,IAAI,CAACoC,QAAQ,CAACC,IAAI,CAACvB,GAAG,CAAC;MACvB,IAAI,CAACvB,aAAa,CAAC+C,UAAU,EAAE;IACnC,CAAC,EAAG7B,KAAK,IAAI;MACTD,OAAO,CAACC,KAAK,CAAC,8BAA8B,EAAEA,KAAK,CAAC;IACxD,CAAC,CAAC;EACN;EAGA8B,UAAU;IACN,IAAI,CAACjB,SAAS,GAAG,EAAE;IACnB,IAAI,CAAC/B,aAAa,CAAC+C,UAAU,EAAE;EACnC;EAEAE,SAAS;IACL,IAAI,CAACC,QAAQ,CAACC,KAAK,EAAE;EACzB;EAAC;qBA5KQrD,0BAA0B;EAAA;EAAA;UAA1BA,0BAA0B;IAAAsD;IAAAC;MAAArC;IAAA;IAAAsC;MAAAT;IAAA;IAAAU;IAAAC;IAAAC;IAAAC;MAAA;QCrBvCC,8BAA0B;QACwBA,YAAwC;;QAAAA,iBAAK;QAC7FA,iCAKC;QAFCA;UAAA,OAASC,gBAAY;QAAA,EAAC;QAGtBD,+BAAyB;QAAAA,sBAAO;QAAAA,iBAAO;QAG3CA,+BAGC;QADCA;UAAA,OAAYC,+BAAuB;QAAA,EAAC;QAEpCD,8BAAkD;QAK9CA;UAAA,OAAUC,+BAAuB;QAAA,EAAC;QACnCD,iBAAc;QAEjBA,+BAA0B;QAItBA,aACF;;QAAAA,iBAAS;;;QA3BqCA,eAAwC;QAAxCA,kEAAwC;QAWxFA,eAAsB;QAAtBA,wCAAsB;QAKlBA,eAAiB;QAAjBA,mCAAiB;QAQnBA,eAA6B;QAA7BA,+CAA6B;QAE3BA,eACF;QADEA,2EACF", "names": ["EventEmitter", "FormGroup", "ModalUpdateConfigComponent", "constructor", "_modalService", "_translateService", "_autoSchedule", "_loadingService", "_toastService", "key", "type", "props", "label", "instant", "placeholder", "required", "validation", "messages", "min", "ngOnInit", "selectedConfig", "console", "error", "show", "getScheduleConfigById", "configId", "subscribe", "res", "log", "beginDate", "data", "begin_date", "Date", "toISOString", "slice", "editModel", "begin_time", "old_begin_time", "match_duration", "break_duration", "break_match_duration", "tournament_id", "location_id", "date", "dismiss", "onSubmitEdit", "model", "updateScheduleConfig", "success", "onSubmit", "emit", "dismissAll", "closeModal", "clearForm", "editForm", "reset", "selectors", "inputs", "outputs", "decls", "vars", "consts", "template", "i0", "ctx"], "sourceRoot": "", "sources": ["D:\\Code\\Work\\ezactive-vn\\ezleague-core\\client\\src\\app\\pages\\league-tournament\\auto-schedule\\modal-update-config\\modal-update-config.component.ts", "D:\\Code\\Work\\ezactive-vn\\ezleague-core\\client\\src\\app\\pages\\league-tournament\\auto-schedule\\modal-update-config\\modal-update-config.component.html"], "sourcesContent": ["import { Component, EventEmitter, Input, OnInit, Output } from '@angular/core';\r\nimport { FormGroup } from '@angular/forms';\r\nimport { FormlyFieldConfig } from '@ngx-formly/core';\r\nimport { NgbModal } from '@ng-bootstrap/ng-bootstrap';\r\nimport { TranslateService } from '@ngx-translate/core';\r\nimport { AutoScheduleService } from '../../../../services/auto-schedule.service';\r\nimport { LoadingService } from 'app/services/loading.service';\r\nimport { ToastrService } from 'ngx-toastr';\r\n\r\nexport type UpdateConfigParams = {\r\n    tournamentId: string;\r\n    location: string;\r\n    date: string;\r\n    configId: number;\r\n}\r\n\r\n@Component({\r\n    selector: 'app-modal-update-config',\r\n    templateUrl: './modal-update-config.component.html',\r\n    styleUrls: ['./modal-update-config.component.scss']\r\n})\r\nexport class ModalUpdateConfigComponent implements OnInit {\r\n\r\n    @Input() selectedConfig: UpdateConfigParams | null = null;\r\n\r\n    editForm = new FormGroup({});\r\n    editModel = {};\r\n\r\n    public editFields: FormlyFieldConfig[] = [\r\n        {\r\n            key: 'begin_date',\r\n            type: 'input',\r\n            props: {\r\n                label: this._translateService.instant('Date'),\r\n                placeholder: this._translateService.instant('Enter begin date'),\r\n                required: true,\r\n                type: 'date'\r\n            }\r\n        },\r\n        {\r\n            key: 'begin_time',\r\n            type: 'input',\r\n            props: {\r\n                label: this._translateService.instant('Begin time'),\r\n                placeholder: this._translateService.instant(\r\n                    'Enter begin time'\r\n                ),\r\n                required: true,\r\n                type: 'time'\r\n            },\r\n            validation: {\r\n                messages: {\r\n                    required: this._translateService.instant('Begin time is required.')\r\n                }\r\n            }\r\n        },\r\n        {\r\n            key: 'old_begin_time',\r\n            type: 'input',\r\n            props: {\r\n                type: 'hidden'\r\n            },\r\n        },\r\n        {\r\n            key: 'match_duration',\r\n            type: 'input',\r\n            props: {\r\n                label: this._translateService.instant('Match duration'),\r\n                placeholder: this._translateService.instant(\r\n                    'Enter match duration (in minutes)'\r\n                ),\r\n                required: true,\r\n                type: 'number',\r\n                min: 1,\r\n            },\r\n            validation: {\r\n                messages: {\r\n                    required: this._translateService.instant('Match duration is required.'),\r\n                    min: this._translateService.instant('Match duration must be at least 1 minute.')\r\n                }\r\n            }\r\n        },\r\n        {\r\n            key: 'break_duration',\r\n            type: 'input',\r\n            props: {\r\n                label: this._translateService.instant('Break duration'),\r\n                placeholder: this._translateService.instant(\r\n                    'Enter break between match duration (in minutes)'\r\n                ),\r\n                required: true,\r\n                type: 'number',\r\n                min: 0,\r\n            },\r\n            validation: {\r\n                messages: {\r\n                    required: this._translateService.instant('Break duration is required.'),\r\n                    min: this._translateService.instant('Break duration must be at least 0 minutes.')\r\n                }\r\n            }\r\n        },\r\n        {\r\n            key: 'location_id',\r\n            type: 'input',\r\n            props: {\r\n                type: 'hidden'\r\n            }\r\n        },\r\n        {\r\n            key: 'tournament_id',\r\n            type: 'input',\r\n            props: {\r\n                type: 'hidden'\r\n            }\r\n        },\r\n        {\r\n            key: 'date',\r\n            type: 'input',\r\n            props: {\r\n                type: 'hidden'\r\n            }\r\n        }\r\n    ];\r\n\r\n    @Output() onSubmit = new EventEmitter();\r\n\r\n    constructor(\r\n        private _modalService: NgbModal,\r\n        private _translateService: TranslateService,\r\n        private _autoSchedule: AutoScheduleService,\r\n        private _loadingService: LoadingService,\r\n        private _toastService: ToastrService\r\n    ) {\r\n\r\n    }\r\n\r\n    ngOnInit() {\r\n        if (!this.selectedConfig) {\r\n            console.error('No selected configuration provided');\r\n            return;\r\n        }\r\n\r\n\r\n        this._loadingService.show();\r\n\r\n        this._autoSchedule.getScheduleConfigById(this.selectedConfig.configId)\r\n            .subscribe((res) => {\r\n                console.log('🚀 ~ ngOnInit ~ res: ', res);\r\n                // Convert ISO date string to 'YYYY-MM-DD' for input[type=\"date\"]\r\n                const beginDate = res.data.begin_date\r\n                    ? new Date(res.data.begin_date).toISOString().slice(0, 10)\r\n                    : '';\r\n\r\n                this.editModel = {\r\n                    ...this.editModel,\r\n                    begin_date: beginDate,\r\n                    begin_time: res.data.begin_time,\r\n                    old_begin_time: `${beginDate} ${res.data.begin_time}`,\r\n                    match_duration: res.data.match_duration,\r\n                    break_duration: res.data.break_match_duration,\r\n                    tournament_id: res.data.tournament_id,\r\n                    location_id: res.data.location_id,\r\n                    date: this.selectedConfig.date,\r\n                };\r\n\r\n                console.log(\"🚀 ~ ModalUpdateConfigComponent ~ .subscribe ~ editModel:\", this.editModel)\r\n\r\n\r\n\r\n                this._loadingService.dismiss();\r\n\r\n            });\r\n    }\r\n\r\n    onSubmitEdit(model) {\r\n        console.log('🚀 ~ onSubmitEdit ~ model: ', model);\r\n        this._autoSchedule.updateScheduleConfig(model).subscribe((res) => {\r\n            this._toastService.success(this._translateService.instant('Schedule configuration updated successfully.'));\r\n            this.onSubmit.emit(res);\r\n            this._modalService.dismissAll();\r\n        }, (error) => {\r\n            console.error('Error scheduling tournament:', error);\r\n        });\r\n    }\r\n\r\n\r\n    closeModal() {\r\n        this.editModel = {};\r\n        this._modalService.dismissAll();\r\n    }\r\n\r\n    clearForm() {\r\n        this.editForm.reset();\r\n    }\r\n\r\n}\r\n", "<div class=\"modal-header\">\r\n  <h5 class=\"modal-title\" id=\"editScheduleConfig\">{{ \"Edit Schedule Config\" | translate }}</h5>\r\n  <button\r\n    type=\"button\"\r\n    class=\"close\"\r\n    (click)=\"closeModal()\"\r\n    aria-label=\"Close\"\r\n  >\r\n    <span aria-hidden=\"true\">&times;</span>\r\n  </button>\r\n</div>\r\n<form\r\n  [formGroup]=\"editForm\"\r\n  (ngSubmit)=\"onSubmitEdit(editModel)\"\r\n>\r\n  <div class=\"modal-body\" tabindex=\"0\" ngbAutofocus>\r\n    <formly-form\r\n      [form]=\"editForm\"\r\n      [fields]=\"editFields\"\r\n      [model]=\"editModel\"\r\n      (submit)=\"onSubmitEdit(editModel)\"\r\n    ></formly-form>\r\n  </div>\r\n  <div class=\"modal-footer\">\r\n    <button type=\"submit\" class=\"w-100 btn btn-primary\" rippleEffect\r\n    [disabled]=\"editForm.invalid\"\r\n    >\r\n      {{ 'Re-schedule Match' | translate }}\r\n    </button>\r\n  </div>\r\n</form>"]}, "metadata": {}, "sourceType": "module", "externalDependencies": []}