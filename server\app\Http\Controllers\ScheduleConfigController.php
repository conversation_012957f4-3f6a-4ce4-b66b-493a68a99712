<?php
namespace App\Http\Controllers;

use App\Models\ScheduleConfig;
use App\Models\Tournament;
use Carbon\Carbon;
use Error;
use Exception;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;

class ScheduleConfigController extends Controller
{

    private $timeSlotController;

    public function __construct()
    {
        $this->timeSlotController = new ScheduleTimeSlotController();
    }

    public function saveConfig($tournamentId, $locationId, $beginDate, $beginTime, $matchDuration, $breakDuration, $timeZone = null, $forceUpdate = false)
    {
        try {
            // Default timezone to UTC if not provided
            $timeZone = $timeZone ?? 'UTC';

            // Convert begin date/time to UTC for consistent storage
            $beginDateTimeLocal  = Carbon::parse($beginDate . ' ' . $beginTime, $timeZone);
            $newBeginDateTimeUTC = $beginDateTimeLocal->utc();

            $newBeginDateUTC = $newBeginDateTimeUTC->format('Y-m-d');
            $newBeginTimeUTC = $newBeginDateTimeUTC->format('H:i:s');

            $currentConfig = ScheduleConfig::where('tournament_id', $tournamentId)
                ->where('location_id', $locationId)
                ->first();

            if ($currentConfig) {
                if ($newBeginDateTimeUTC < $currentConfig->begin_time || $forceUpdate) {
                    $currentConfig->update([
                        'begin_date'           => $newBeginDateUTC,
                        'begin_time'           => $newBeginTimeUTC,
                        'match_duration'       => $matchDuration,
                        'break_match_duration' => $breakDuration,
                    ]);
                }
            } else {
                ScheduleConfig::create([
                    'tournament_id'        => $tournamentId,
                    'location_id'          => $locationId,
                    'begin_date'           => $newBeginDateUTC,
                    'begin_time'           => $newBeginTimeUTC,
                    'match_duration'       => $matchDuration,
                    'break_match_duration' => $breakDuration,
                ]);
            }
        } catch (Exception $error) {
            Log::error('$error', [$error]);

            throw new Error($error);
        }
    }

    public function updateOrCreateConfig($tournamentId, $locationId, $beginDate, $newBeginTime, $endTimeOfDate, $matchDuration, $breakDuration, $timeZone = null, $forceUpdate = false)
    {

        $timeZone = $timeZone ?? 'UTC';

        // Convert begin date/time to UTC for consistent storage
        $beginDateTimeLocal  = Carbon::parse($beginDate . ' ' . $newBeginTime, $timeZone);
        $newBeginDateTimeUTC = $beginDateTimeLocal->utc();

        if ($endTimeOfDate) {
            $endTimeOfDateLocal = Carbon::parse($beginDate . ' ' . $endTimeOfDate, $timeZone);
            $endTimeOfDateUTC   = $endTimeOfDateLocal->utc();
        }

        $newBeginDateUTC = $newBeginDateTimeUTC->format('Y-m-d');
        $newBeginTimeUTC = $newBeginDateTimeUTC->format('H:i:s');

        try {
            $currentConfig = ScheduleConfig::where('tournament_id', $tournamentId)
                ->where('location_id', $locationId)
                ->where('begin_date', $newBeginDateUTC)
                ->first();

            if ($currentConfig) {
                if ($newBeginDateTimeUTC < $currentConfig->begin_time || $forceUpdate) {
                    $currentConfig->update([
                        'begin_date'           => $newBeginDateUTC,
                        'begin_time'           => $newBeginTimeUTC,
                        'end_time'             => $endTimeOfDate ? $endTimeOfDateUTC : null, // need to find way change
                        'match_duration'       => $matchDuration,
                        'break_match_duration' => $breakDuration,
                    ]);
                }
            } else {
                ScheduleConfig::create([
                    'tournament_id'        => $tournamentId,
                    'location_id'          => $locationId,
                    'begin_date'           => $newBeginDateUTC,
                    'begin_time'           => $newBeginTimeUTC,
                    'end_time'             => $endTimeOfDate ? $endTimeOfDateUTC : null,
                    'match_duration'       => $matchDuration,
                    'break_match_duration' => $breakDuration,
                ]);
            }
        } catch (Exception $error) {
            Log::error('error in saveOrUpdateConfig', [$error]);
            throw new Error($error->getMessage());
        }

    }

    public function getScheduleConfig(Request $request)
    {
        try {

            $request->validate([
                'tournament_id' => 'required|integer',
                'location'      => 'required|string',
                'date'          => 'required|date_format:Y-m-d',
            ]);

            $config = ScheduleConfig::where('tournament_id', $request->get('tournament_id'))
                ->where('location_id', $request->get('location'))
                ->where('begin_date', $request->get('date'))
                ->first();

            // add time by header x-time-zone before response
            if ($config) {
                $timezone           = request()->header('X-Time-Zone') ?? 'UTC';
                $config->begin_date = Carbon::parse($config->begin_date, 'UTC')->setTimezone($timezone)->format('Y-m-d H:i:s');
                $config->begin_time = Carbon::parse($config->begin_time, 'UTC')->setTimezone($timezone)->format('H:i:s');
            }

            return response()->json([
                'success' => true,
                'data'    => $config,
            ]);
        } catch (Exception $error) {
            Log::error('error in getScheduleConfig', [$error]);

            return response()->json([
                'message' => $error->getMessage(),
            ], 500);
        }
    }

    public function updateScheduleConfig(Request $request)
    {
        try {
            $request->validate([
                'tournament_id'  => 'required|integer',
                'location_id'    => 'required|integer',
                'begin_date'     => 'required|date',
                'begin_time'     => ['required', 'regex:/^\d{2}:\d{2}(:\d{2})?$/'],
                'old_begin_time' => 'required|date_format:Y-m-d H:i:s',
                'match_duration' => 'required|integer|min:1',
                'break_duration' => 'required|integer|min:0',
            ]);

            $this->updateOrCreateConfig(
                $request->get('tournament_id'),
                $request->get('location_id'),
                $request->get('begin_date'),
                $request->get('begin_time'),
                null, // need to find way change
                $request->get('match_duration'),
                $request->get('break_duration'),
                $request->header('X-Time-Zone') ?? 'UTC',
                true,
                Carbon::parse($request->get('old_begin_time'), $request->header('X-Time-Zone') ?? 'UTC')->utc()->format('H:i:s')
            );

            Log::info('xyvsajhfaskdhfkalsd', [Carbon::parse($request->get('begin_date') . ' ' . $request->get('begin_time'), $request->header('X-Time-Zone') ?? 'UTC')->utc()]);

            $this->timeSlotController->regenerateTimeSlots(
                $request->get('tournament_id'),
                $request->get('location_id'),
                Carbon::parse($request->get('begin_date') . ' ' . $request->get('begin_time'), $request->header('X-Time-Zone') ?? 'UTC')->utc()->format('Y-m-d'),
                Carbon::parse($request->get('begin_date') . ' ' . $request->get('begin_time'), $request->header('X-Time-Zone') ?? 'UTC')->utc()->format('H:i:s'),
            );

            return response()->json([
                'success' => true,
                'message' => 'Schedule configuration updated successfully.',
            ]);
        } catch (Exception $error) {

            Log::error('Error updating schedule config', [$error]);

            return response()->json([
                'message' => $error->getMessage(),
            ], 500);
        }
    }

    public function updateScheduleStatus($tournamentId)
    {
        try {
            $currentTournament = Tournament::find($tournamentId);

            if (! $currentTournament) {
                return response()->json([
                    'status'  => "error",
                    'message' => 'Tournament not found',
                ], 400);
            }

            $currentTournament->is_locked_schedule = ! $currentTournament->is_locked_schedule;
            $currentTournament->save();

            return response()->json([
                'status'  => "success",
                'message' => 'Update schedule status successfully',
                'data'    => $currentTournament->is_locked_schedule,
            ]);
        } catch (Exception $error) {
            Log::error('Error updating schedule config', [$error]);
            return response()->json([
                'message' => $error->getMessage(),
            ]);
        }
    }

    public function clearUnusedConfigs($tournamentId)
    {
        try {
            // let make elequent query that find all the location in configs that not have any matches or time slot
            ScheduleConfig::where('tournament_id', $tournamentId)
                ->whereDoesntHave('scheduleMatches')
                ->whereDoesntHave('scheduleTimeSlots')
                ->delete();

        } catch (Exception $error) {
            Log::error('Error removing unused schedule config', [$error]);
            return response()->json([
                'message' => $error->getMessage(),
            ]);
        }
    }

    public function clearAllConfigs($tournamentId)
    {
        try {
            ScheduleConfig::where('tournament_id', $tournamentId)->delete();
        } catch (Exception $error) {
            Log::error('Error removing all schedule config', [$error]);
            return response()->json([
                'message' => $error->getMessage(),
            ]);
        }
    }
}
